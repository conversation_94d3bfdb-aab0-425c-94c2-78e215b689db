<template>
  <div class="layout-container" style="height: 100vh">
    <div style="height: 96%;">
      <el-container style="height: 100%">
        <el-aside width="200px" style="background-color: #061031; height: 100%">
          <img src="/images/logo1.jpg" alt="" style="width: 100%; margin-bottom: 10px">
          <el-menu :default-active="activeMenu" class="el-menu-vertical-demo" background-color="#061031"
            text-color="#fff" active-text-color="#ffd04b" router>
            <el-menu-item index="/dashboard">
              <el-icon>
                <Menu />
              </el-icon>
              <span>首页</span>
            </el-menu-item>

            <el-menu-item index="/dashboard">
              <el-icon>
                <Menu />
              </el-icon>
              <span>Dashboard</span>
            </el-menu-item>

            <el-menu-item v-if="userInfo.userLevel === '1'" index="/user-management">
              <el-icon>
                <User />
              </el-icon>
              <span>用户管理</span>
            </el-menu-item>

            <el-sub-menu index="data">
              <template #title>
                <el-icon>
                  <DataBoard />
                </el-icon>
                <span>数据管理&预处理</span>
              </template>
              <el-menu-item index="/data/management">
                <span>流数据管理</span>
              </el-menu-item>
              <el-menu-item index="/data/dataset">
                <span>数据集管理</span>
              </el-menu-item>
              <el-menu-item index="/data/process">
                <span>数据预处理</span>
              </el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="model">
              <template #title>
                <el-icon>
                  <Setting />
                </el-icon>
                <span>模型管理</span>
              </template>
              <el-menu-item index="/model/rules">
                <span>规则管理</span>
              </el-menu-item>
              <el-menu-item v-if="userInfo.userLevel === '1'" index="/model/training-admin">
                <span>模型训练</span>
              </el-menu-item>
              <el-menu-item v-else index="/model/training-user">
                <span>模型训练</span>
              </el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="risk">
              <template #title>
                <el-icon>
                  <Warning />
                </el-icon>
                <span>风险评估</span>
              </template>
              <el-menu-item index="/risk/rule">
                <span>规则评估</span>
              </el-menu-item>
              <el-menu-item index="/risk/model">
                <span>平台模型评估</span>
              </el-menu-item>
              <el-menu-item index="/risk/selfmodel">
                <span>自训练模型评估</span>
              </el-menu-item>
              <el-menu-item index="/risk/fusion">
                <span>规则+模型融合评估</span>
              </el-menu-item>
            </el-sub-menu>

            <el-menu-item index="/user-alert">
              <el-icon>
                <Bell />
              </el-icon>
              <span>用户预警</span>
            </el-menu-item>

            <el-sub-menu index="fraud-simulation">
              <template #title>
                <el-icon>
                  <DataAnalysis />
                </el-icon>
                <span>金融欺诈流数据模拟</span>
              </template>
              <el-menu-item index="/fraud-simulation/simulation">
                <span>金融欺诈流数据模拟</span>
              </el-menu-item>
              <el-menu-item index="/fraud-simulation/stream-generation">
                <span>流数据生成与处理</span>
              </el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="real-time-analysis">
              <template #title>
                <el-icon>
                  <TrendCharts />
                </el-icon>
                <span>金融欺诈实时数据分析</span>
              </template>
              <el-menu-item index="/real-time-analysis/preprocessing">
                <span>流数据预处理</span>
              </el-menu-item>
              <el-menu-item index="/real-time-analysis/rule-training">
                <span>风险评估规则/决策训练</span>
              </el-menu-item>
              <el-menu-item index="/real-time-analysis/model-deployment">
                <span>模型发布与决策训练</span>
              </el-menu-item>
              <el-menu-item index="/real-time-analysis/assessment">
                <span>风险实时评估与预警</span>
              </el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="monitor">
              <template #title>
                <el-icon>
                  <Monitor />
                </el-icon>
                <span>平台监控</span>
              </template>
              <el-menu-item>
                <a style="color: #fff" href="http://10.16.9.145:9870" target="_blank">HDFS</a>
              </el-menu-item>
              <el-menu-item>
                <a style="color: #fff" href="http://10.16.30.215:8088" target="_blank">YARN</a>
              </el-menu-item>
              <el-menu-item>
                <a style="color: #fff" href="http://10.16.9.145:19888" target="_blank">MapReduce</a>
              </el-menu-item>
            </el-sub-menu>
          </el-menu>
        </el-aside>

        <el-container>
          <el-header style="font-size: 12px; background-color: #061031; color: white;">
            <el-row :gutter="20">
              <el-col :span="16">
                <div style="font-size: 24px; font-weight: bold; margin-left: 50%; letter-spacing: 3px;">
                  面向金融行业流数据的在线分析系统
                </div>
              </el-col>
              <el-col :span="8">
                <div class="user-wrapper">
                  <el-dropdown>
                    <el-icon style="margin-right: 15px">
                      <Setting />
                    </el-icon>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="logout">注销</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                  <span>{{ userInfo.username }}</span>
                </div>
              </el-col>
            </el-row>
          </el-header>

          <el-main style="padding: 10px !important; background: transparent;">
            <router-view />
          </el-main>
        </el-container>
      </el-container>
    </div>

    <div
      style="background-color: #acabad; height: 4%; text-align: center; color: #2a2831; font-size: 16px; font-weight: bold; line-height: 40px">
      重庆邮电大学 大数据智能计算创新研发团队@2015-2023星环信息科技(上海)股份有限公司&重庆邮电大学联合出品
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Menu, User, DataBoard, Setting, Warning, Bell, Monitor, DataAnalysis, TrendCharts
} from '@element-plus/icons-vue'
import axios from 'axios'

const router = useRouter()
const route = useRoute()

const userInfo = ref({})

const activeMenu = computed(() => {
  return route.path
})

const logout = async () => {
  try {
    await axios.post('/logout')
    localStorage.removeItem('userInfo')
    router.push('/login')
  } catch (error) {
    console.error('Logout error:', error)
    localStorage.removeItem('userInfo')
    router.push('/login')
  }
}

onMounted(() => {
  const storedUserInfo = localStorage.getItem('userInfo')
  if (storedUserInfo) {
    userInfo.value = JSON.parse(storedUserInfo)
  }
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.el-header {
  background-color: #B3C0D1;
  color: #333;
  line-height: 60px;
}

.el-aside {
  color: #333;
}

.el-main {
  padding: 10px !important;
  background: transparent;
}

.user-wrapper {
  display: flex;
  align-items: center;
  /* 垂直居中对齐元素 */
  justify-content: flex-end;
  /* 保持原来的右对齐 */
  height: 60px;
  /* 必须有高度，或者继承父容器高度 */
}
</style>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>金融在线分析系统</title>
    <link rel="stylesheet" href="css/login.css">
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
<div id="app">
    <el-row :gutter="20">
        <el-col :span="5">
            <div class="logo">
                <img src="images/logo1.jpg" alt=""/>
            </div>
        </el-col>
        <el-col :span="15">
            <div style="height: 70px"></div>
            <span class="title">面向金融行业流数据的在线分析系统</span>
        </el-col>

    </el-row>

    <div class="login-form">
        <div class="content">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="用户名" prop="username">
                    <el-input v-model="ruleForm.username" placeholder="请输入用户名"></el-input>
                </el-form-item>
                <el-form-item label="密码" prop="password">
                    <el-input type="password" v-model="ruleForm.password" placeholder="请输入密码"></el-input>
                </el-form-item>
                <el-form-item label="验证码" prop="code">
                    <el-input v-model="ruleForm.code" placeholder="请输入验证码"></el-input>
                </el-form-item>
                <el-form-item>
                    <img style="width:160px;height:60px;"
                         :src="imgSrc" @click="getCaptcha()"/>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleForm')">立即登录</el-button>
                    <el-button @click="resetForm('ruleForm')">重置</el-button>
                    <el-button type="warn" @click="register()">注册</el-button>
                </el-form-item>
            </el-form>
        </div>

    </div>
</div>

</body>
<script src="js/vue.js"></script>
<script src="js/axios.js"></script>
<script src="js/elementui.js"></script>
<script>
    let app = new Vue({
        el: "#app",
        data() {
            return {
                imgSrc: "/captcha/generate",
                ruleForm: {
                    username: '',
                    password: '',
                    uuid: '',
                    code: '',
                },
                rules: {
                    username: [
                        {required: true, message: '请输入用户名', trigger: 'blur'},
                    ],
                    password: [
                        {required: true, message: '请输入密码', trigger: 'blur'}
                    ],
                    code: [
                        {required: true, message: '请输入验证码', trigger: 'blur'}
                    ],
                }
            };
        },
        created() {
            this.getCaptcha();
        },
        methods: {
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {

                        //登录
                        axios.post(`/login`, this.ruleForm).then(response => {
                            if (response.data.code == 1) {//1表示登录成功
                                localStorage.setItem('userInfo', JSON.stringify(response.data.data))
                                window.location.href = '/'
                            } else {
                                this.$message.error(response.data.msg)
                            }
                        })

                    } else {
                        this.$message.error("输入有误")
                        return false;
                    }
                });
            },
            resetForm(formName) {
                this.$refs[formName].resetFields();
            },
            generateRandomString(length = 11) {
                let charset = "abcdefghijklmnopqrstuvwxyz-_ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
                let values = new Uint32Array(length);
                window.crypto.getRandomValues(values);
                let str = "";
                for (let i = 0; i < length; i++) {
                    str += charset[values[i] % charset.length];
                }
                return str;
            },
            getCaptcha() {
                this.ruleForm.uuid = sessionStorage.getItem("uuid");
                if (!this.ruleForm.uuid) {
                    this.ruleForm.uuid = this.generateRandomString();
                    sessionStorage.setItem("uuid", this.ruleForm.uuid)
                }
                this.imgSrc = "/captcha/generate?uuid="
                    + this.ruleForm.uuid + "&v=" + new Date().getTime();
            },
            register(){
                window.location.href = '/register.html'
            }
        }
    });
</script>
</html>
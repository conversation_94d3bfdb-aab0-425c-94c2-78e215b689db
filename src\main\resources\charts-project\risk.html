<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/elementui.css">
    <style>
        body {
            background-color: #fff;
        }
        #app {
            padding: 20px;
        }

        .form {
            width: 1200px;
            margin: 55px auto;
        }

    </style>
</head>
<body>
<div id="app">

    <div class="bread">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item><a href="/">首页</a></el-breadcrumb-item>
            <el-breadcrumb-item><a href="/">风险评估</a></el-breadcrumb-item>
        </el-breadcrumb>
    </div>

    <div class="form">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" class="demo-ruleForm">
            <el-form-item label="评估方法" prop="method" placeholder="请选择评估方法" >
                <el-cascader
                        style="width: 400px"
                        v-model="ruleForm.method"
                        :options="options"
                        :props="{ expandTrigger: 'hover' }"
                        @change="methodChange">
                </el-cascader>
            </el-form-item>

            <el-form-item label="评估环境" prop="env">
                <el-select v-model="ruleForm.env" placeholder="请选择评估环境" style="width: 400px">
                    <el-option label="后台数据库" value="env1"></el-option>
                    <el-option label="大数据平台" value="env2"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="请选择评估数据集">
                <el-table
                        ref="multipleTable"
                        :data="tableData"
                        tooltip-effect="dark"
                        style="width: 100%"
                        @selection-change="handleSelectionChange"
                        max-height="550">
                    <el-table-column
                            type="selection"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            prop="streamTime"
                            label="消费时间"
                            width="280">
                    </el-table-column>
                    <el-table-column
                            prop="streamMoney"
                            label="消费金额(元)"
                            width="280">
                    </el-table-column>
                    <el-table-column
                            prop="streamSignLocation"
                            label="注册地"
                            width="280"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="streamConsumeLocation"
                            label="消费地"
                    >
                    </el-table-column>
                </el-table>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="submitForm('ruleForm')">立即评估</el-button>
                <el-button @click="resetForm('ruleForm')">重置</el-button>
            </el-form-item>
        </el-form>
    </div>


</div>
</body>
<script src="js/vue.js"></script>
<script src="js/axios.js"></script>
<script src="js/elementui.js"></script>
<script>
    new Vue({
        el: "#app",
        data() {
            return {
                tableData: [
                    {
                        streamTime: '2016-05-02',
                        streamMoney: '60591',
                        streamConsumeLocation: '广东省-广州市',
                        streamSignLocation: '江苏省',
                    },{
                        streamTime: '2016-05-02',
                        streamMoney: '60591',
                        streamConsumeLocation: '广东省-广州市',
                        streamSignLocation: '江苏省',
                    },{
                        streamTime: '2016-05-02',
                        streamMoney: '60591',
                        streamConsumeLocation: '广东省-广州市',
                        streamSignLocation: '江苏省',
                    },{
                        streamTime: '2016-05-02',
                        streamMoney: '60591',
                        streamConsumeLocation: '广东省-广州市',
                        streamSignLocation: '江苏省',
                    },{
                        streamTime: '2016-05-02',
                        streamMoney: '60591',
                        streamConsumeLocation: '广东省-广州市',
                        streamSignLocation: '江苏省',
                    },{
                        streamTime: '2016-05-02',
                        streamMoney: '60591',
                        streamConsumeLocation: '广东省-广州市',
                        streamSignLocation: '江苏省',
                        streamIsNormal: 0
                    },{
                        streamTime: '2016-05-02',
                        streamMoney: '60591',
                        streamConsumeLocation: '广东省-广州市',
                        streamSignLocation: '江苏省',
                    },{
                        streamTime: '2016-05-02',
                        streamMoney: '60591',
                        streamConsumeLocation: '广东省-广州市',
                        streamSignLocation: '江苏省',
                    },{
                        streamTime: '2016-05-02',
                        streamMoney: '60591',
                        streamConsumeLocation: '广东省-广州市',
                        streamSignLocation: '江苏省',
                    },{
                        streamTime: '2016-05-02',
                        streamMoney: '60591',
                        streamConsumeLocation: '广东省-广州市',
                        streamSignLocation: '江苏省',
                    },{
                        streamTime: '2016-05-02',
                        streamMoney: '60591',
                        streamConsumeLocation: '广东省-广州市',
                        streamSignLocation: '江苏省',
                    }
                ],
                options: [
                    {
                        value: 'rule',
                        label: '规则',
                        children: [
                            {
                                value: 'rule1',
                                label: '60s消费间隔'
                            }, {
                                value: 'rule2',
                                label: '异地消费间隔'
                            }, {
                                value: 'rule3',
                                label: '消费频次'
                            }
                        ]
                    },
                    {
                        value: 'model',
                        label: '模型',
                        children: [
                            {
                                value: 'model1',
                                label: 'K-近邻算法'
                            },
                            {
                                value: 'model2',
                                label: '线性回归'
                            },
                            {
                                value: 'model3',
                                label: '逻辑回归'
                            },
                            {
                                value: 'model4',
                                label: '决策树'
                            }
                        ]
                    }
                ],
                ruleForm: {
                    method: '',
                    env: '',
                    date1: '',
                    date2: '',
                    delivery: false,
                    type: [],
                    resource: '',
                    desc: ''
                },
                rules: {
                    method: [
                        { required: true, message: '请选择评估方法', trigger: 'change' }
                    ],
                    env: [
                        { required: true, message: '请选择评估环境', trigger: 'change' }
                    ]
                }
            }
        },
        methods: {
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        location.href="risk-result.html";
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            resetForm(formName) {
                this.$refs[formName].resetFields();
            },
            methodChange(){
                console.log(this.ruleForm.method);
            }
        },
    });
</script>
</html>
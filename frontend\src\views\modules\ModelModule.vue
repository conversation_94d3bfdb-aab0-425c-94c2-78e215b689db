<template>
  <div class="model-module">
    <el-card>
      <template #header>
        <div class="module-header">
          <h2>模型管理</h2>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>模型管理</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </template>

      <!-- 子模块导航 -->
      <div class="sub-navigation">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="规则管理" name="rules">
            <template #label>
              <span class="tab-label">
                <el-icon><List /></el-icon>
                规则管理
              </span>
            </template>
          </el-tab-pane>
          <el-tab-pane label="模型训练(管理员)" name="training-admin" v-if="isAdmin">
            <template #label>
              <span class="tab-label">
                <el-icon><Tools /></el-icon>
                模型训练(管理员)
              </span>
            </template>
          </el-tab-pane>
          <el-tab-pane label="模型训练(用户)" name="training-user">
            <template #label>
              <span class="tab-label">
                <el-icon><User /></el-icon>
                模型训练(用户)
              </span>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 子页面内容 -->
      <div class="module-content">
        <router-view />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { List, Tools, User } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const activeTab = ref('rules')

// 检查是否为管理员
const isAdmin = computed(() => {
  const userInfo = localStorage.getItem('userInfo')
  if (userInfo) {
    const user = JSON.parse(userInfo)
    return user.userLevel === '1' // 假设1为管理员
  }
  return false
})

// 根据当前路由设置活跃标签
const setActiveTabFromRoute = () => {
  const routeName = route.name
  switch (routeName) {
    case 'RuleManagement':
      activeTab.value = 'rules'
      break
    case 'ModelAdmin':
      activeTab.value = 'training-admin'
      break
    case 'ModelUser':
      activeTab.value = 'training-user'
      break
    default:
      activeTab.value = 'rules'
  }
}

// 当前页面标题
const currentPageTitle = computed(() => {
  switch (activeTab.value) {
    case 'rules':
      return '规则管理'
    case 'training-admin':
      return '模型训练(管理员)'
    case 'training-user':
      return '模型训练(用户)'
    default:
      return '规则管理'
  }
})

// 标签切换处理
const handleTabChange = (tabName) => {
  switch (tabName) {
    case 'rules':
      router.push('/model/rules')
      break
    case 'training-admin':
      router.push('/model/training-admin')
      break
    case 'training-user':
      router.push('/model/training-user')
      break
  }
}

// 监听路由变化
watch(() => route.name, () => {
  setActiveTabFromRoute()
}, { immediate: true })
</script>

<style scoped>
.model-module {
  height: 100%;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.module-header h2 {
  margin: 0;
  color: #333;
}

.sub-navigation {
  margin-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.module-content {
  min-height: 500px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-tabs__header) {
  margin: 0 0 20px 0;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__item) {
  padding: 0 20px;
  height: 50px;
  line-height: 50px;
  font-size: 14px;
}

:deep(.el-tabs__item.is-active) {
  color: #409EFF;
  font-weight: 600;
}
</style>

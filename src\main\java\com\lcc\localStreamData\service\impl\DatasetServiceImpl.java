package com.lcc.localStreamData.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lcc.localStreamData.entity.Dataset;
import com.lcc.localStreamData.entity.StreamData;
import com.lcc.localStreamData.mapper.DatasetMapper;
import com.lcc.localStreamData.service.DatasetService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class DatasetServiceImpl extends ServiceImpl<DatasetMapper, Dataset> implements DatasetService {
    @Override
    public Page<Dataset> findList(int page, int pageSize, String datasetName) {
        //构造分页构造器
        Page pageInfo = new Page(page, pageSize);
        //构造条件构造器
        QueryWrapper<Dataset> queryWrapper = new QueryWrapper<>();
        //添加过滤条件
        if (StringUtils.isNotEmpty(datasetName)) {
            queryWrapper.like("dataset_name", datasetName);
        }
        //添加排序条件
        queryWrapper.orderByDesc("create_time");

        //执行查询
        Page result = this.page(pageInfo, queryWrapper);

        return result;
    }
}

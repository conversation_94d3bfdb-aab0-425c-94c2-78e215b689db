<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/elementui.css">
    <style>
        body {
            background-color: #fff;
        }
        #app {
            padding: 20px;
        }
        .page {
            margin-top: 30px;
            text-align: center;
        }
        .header {

        }
        .header .search {
            width: 230px;
        }
        .table {
            border: 2px solid #f3f4f7;
            margin-top: 20px;
        }
        .bread {
            margin-bottom: 20px;
        }
        .switch {
            margin-top: 8px;
        }
        .addButton {
            text-align: right;
        }
    </style>
</head>
<body>
<div id="app">

    <div class="bread">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item><a href="/">首页</a></el-breadcrumb-item>
            <el-breadcrumb-item><a href="/">用户管理</a></el-breadcrumb-item>
        </el-breadcrumb>
    </div>

    <div class="header">
        <el-row :gutter="20">
            <el-col :span="8">
                <div class="search">
                    <el-input
                            placeholder="请输入用户名"
                            v-model="username"
                            clearable
                            @keyup.enter.native="handleSearch"

                    >
                        <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
                    </el-input>
                </div>
            </el-col>
            <el-col :span="4" :offset="12">
                <div class="addButton">
                    <el-button type="primary" plain icon="el-icon-plus" @click="addFormVisible = true">新增用户</el-button>
                </div>
            </el-col>
        </el-row>


    </div>
    <div class="table">
        <el-table
                :data="tableData"
                stripe
                style="width: 100%">
            <el-table-column
                    type="index"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="createTime"
                    label="创建时间"
                    width="320">
            </el-table-column>
            <el-table-column
                    prop="username"
                    label="用户名"
                    width="250">
            </el-table-column>
            <el-table-column
                    label="账号级别"
                    width="280"
            >
                <template slot-scope="scope">
                    <el-tag type="success" v-if="scope.row.userLevel=='1'">超级管理员</el-tag>
                    <el-tag type="danger" v-if="scope.row.userLevel=='0'">普通用户</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    label="是否激活"
                    width="230"
            >
                <template slot-scope="scope">
                    <!--开关组件-->
                    <el-switch
                            v-model="scope.row.isActive=='1'"
                            active-color="#13ce66"
                            inactive-color="#ff4949"
                            disabled>
                    </el-switch>
                </template>
            </el-table-column>
            <el-table-column
                    label="操作"
            >
                <template slot-scope="scope">
                    <el-button type="text" :disabled="scope.row.isActive=='1'" @click="handleActive(scope.row.id)">激活</el-button>
                    <el-button type="text" :disabled="scope.row.isActive=='0'" @click="handleLock(scope.row.id)">禁用</el-button>
                    <el-button type="text" @click="handleUpdate(scope.row.id)">修改</el-button>
                    <el-button type="text" @click="handleDelete(scope.row.id)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>

    <div class="page">
        <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="page"
                :page-sizes="[9, 20, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalData">
        </el-pagination>
    </div>

    <el-dialog title="新增用户" :visible.sync="addFormVisible" width="500px">
        <el-form :model="userAddInfo" :rules="rules">
            <el-form-item label="用户名" :label-width="formLabelWidth" prop="username">
                <el-input v-model="userAddInfo.username" autocomplete="off" placeholder="请填写用户名"></el-input>
            </el-form-item>
            <el-form-item label="密码" :label-width="formLabelWidth" prop="password">
                <el-input v-model="userAddInfo.password" autocomplete="off" placeholder="请填写密码" type="password"></el-input>
            </el-form-item>
            <el-form-item label="状态" :label-width="formLabelWidth" >
                    <el-switch
                            style="display: block;margin-top: 9px"
                            v-model="userAddInfo.isActive"
                            active-color="#13ce66"
                            inactive-color="#ff4949"
                            active-text="激活"
                            inactive-text="禁用"
                            active-value="1"
                            inactive-value="0">
                    </el-switch>

            </el-form-item>
            <el-form-item label="用户类型" :label-width="formLabelWidth">
                <el-select v-model="userAddInfo.userLevel" placeholder="请选择用户类型">
                    <el-option label="超级管理员" value="1"></el-option>
                    <el-option label="普通用户" value="0"></el-option>
                </el-select>
            </el-form-item>



        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="addUser">提 交</el-button>
            <el-button @click="handleCancel">取 消</el-button>
        </div>
    </el-dialog>
    <el-dialog title="修改用户" :visible.sync="updateFormVisible" width="500px">
        <el-form :model="userUpdateInfo" :rules="rules">
            <el-form-item label="用户名" :label-width="formLabelWidth" prop="username">
                <el-input v-model="userUpdateInfo.username" autocomplete="off" placeholder="请填写用户名"></el-input>
            </el-form-item>
            <el-form-item label="密码" :label-width="formLabelWidth" prop="password">
                <el-input v-model="userUpdateInfo.password" autocomplete="off" placeholder="请填写密码" type="password"></el-input>
            </el-form-item>
            <el-form-item label="状态" :label-width="formLabelWidth" >
                <el-switch
                        style="display: block;margin-top: 9px"
                        v-model="userUpdateInfo.isActive"
                        active-color="#13ce66"
                        inactive-color="#ff4949"
                        active-text="激活"
                        inactive-text="禁用"
                        active-value="1"
                        inactive-value="0">
                </el-switch>

            </el-form-item>
            <el-form-item label="用户类型" :label-width="formLabelWidth">
                <el-select v-model="userUpdateInfo.userLevel" placeholder="请选择用户类型">
                    <el-option label="超级管理员" value="1"></el-option>
                    <el-option label="普通用户" value="0"></el-option>
                </el-select>
            </el-form-item>



        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updateUser">提 交</el-button>
            <el-button @click="updateFormVisible = false">取 消</el-button>
        </div>
    </el-dialog>
</div>
</body>
<script src="js/vue.js"></script>
<script src="js/axios.js"></script>
<script src="js/elementui.js"></script>
<script src="js/moment.js"></script>
<script>
    new Vue({
        el: "#app",
        data() {
            return {
                tableData: [
                    {
                        username:"admin",
                        createTime:"2023-09-05 23:59:12",
                        userLevel:"1",
                        isActive:"1",
                    },
                    {
                        username:"admin",
                        createTime:"2023-09-05 23:59:12",
                        userLevel:"1",
                        isActive:"1",
                    },
                    {
                        username:"admin",
                        createTime:"2023-09-05 23:59:12",
                        userLevel:"1",
                        isActive:"1",
                    },
                    {
                        username:"admin",
                        createTime:"2023-09-05 23:59:12",
                        userLevel:"1",
                        isActive:"1",
                    },
                    {
                        username:"admin",
                        createTime:"2023-09-05 23:59:12",
                        userLevel:"1",
                        isActive:"1",
                    },
                    {
                        username:"admin",
                        createTime:"2023-09-05 23:59:12",
                        userLevel:"1",
                        isActive:"1",
                    },
                    {
                        username:"admin",
                        createTime:"2023-09-05 23:59:12",
                        userLevel:"1",
                        isActive:"1",
                    },
                    {
                        username:"admin",
                        createTime:"2023-09-05 23:59:12",
                        userLevel:"1",
                        isActive:"1",
                    },
                    {
                        username:"admin",
                        createTime:"2023-09-05 23:59:12",
                        userLevel:"1",
                        isActive:"1",
                    }
                ],
                page: 1,
                pageSize: 9,
                totalData: 400,
                condition: "",
                username: "",
                opt: "",
                userAddInfo: {
                    username:"",
                    password:"",
                    isActive:"1",
                    userLevel:"1"
                },
                userUpdateInfo: {
                    username:"",
                    password:"",
                    isActive:"1",
                    userLevel:"1"
                },
                formLabelWidth: '90px',
                addFormVisible: false,
                updateFormVisible: false,
                rules: {
                    username: [
                        { required: true, message: '请填写用户名', trigger: 'blur' }
                    ],
                    password: [
                        { required: true, message: '请填写密码', trigger: 'blur' }
                    ]
                }
            }
        },
        methods: {
            handleSizeChange(val) {
                this.pageSize=val;
                this.page=1;
                this.getList();
            },
            handleCurrentChange(val) {
                this.page=val;
                this.getList();
            },
            handleSearch() {
                this.page=1;
                this.getList();
            },
            handleCancel() {
                this.userAddInfo = {
                    username:"",
                    password:"",
                    isActive:"1",
                    userLevel:"1"
                }
                this.addFormVisible = false;
            },
            handleActive(id) {
                this.$confirm('此操作将用户账号激活, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {


                    axios.get(`/user/active?id=${id}`).then(response => {

                        if (response.data.code==1){
                            this.$message({
                                type: 'success',
                                message: '操作成功!'
                            });
                            this.getList()
                        }else {
                            this.$message.error('操作失败');
                        }


                    })
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消操作'
                    });
                });
            },
            handleLock(id) {
                this.$confirm('此操作将用户账号禁用, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {


                    axios.get(`/user/lock?id=${id}`).then(response => {

                        if (response.data.code==1){
                            this.$message({
                                type: 'success',
                                message: '操作成功!'
                            });
                            this.getList()
                        }else {
                            this.$message.error('操作失败');
                        }
                    })
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消操作'
                    });
                });
            },
            handleDelete(id) {
                this.$confirm('此操作将永久删除该消费, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error'
                }).then(() => {
                    axios.get(`/user/delete?id=${id}`).then(response => {

                        if (response.data.code==1){
                            this.$message({
                                type: 'success',
                                message: '操作成功!'
                            });
                            this.getList()
                        }else {
                            this.$message.error('操作失败');
                        }
                    })
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            },
            handleUpdate(id) {
                axios.get(`/user/findById?id=${id}`).then(response => {
                    this.userUpdateInfo = response.data.data
                    this.updateFormVisible = true;
                })
            },
            goBack() {
                console.log('go back');
            },
            getList() {
                axios.get(`/user/list?page=${this.page}&pageSize=${this.pageSize}&username=${this.username}`)
                    .then(response => {
                        let data = response.data.data;
                        //格式转换
                        for (let i = 0; i < data.records.length; i++) {
                            data.records[i].createTime = moment(data.records[i].createTime).format('YYYY-MM-DD HH:mm:ss');
                        }
                        this.totalData = data.total;
                        this.tableData = data.records;
                    })
            },
            addUser() {
                axios.post(`/user/save`,this.userAddInfo)
                    .then(response => {
                        if (response.data.code==1){
                            this.$message({
                                type: 'success',
                                message: '添加成功!'
                            });
                            this.getList()
                        }else {
                            this.$message.error('添加失败');
                        }
                        this.handleCancel()
                    })
            },
            updateUser() {
                axios.post(`/user/update`,this.userUpdateInfo)
                    .then(response => {
                        if (response.data.code==1){
                            this.$message({
                                type: 'success',
                                message: '修改成功!'
                            });
                            this.getList()
                        }else {
                            this.$message.error('添加失败');
                        }
                        this.updateFormVisible = false;
                    })
            },
        },
        created() {
            this.getList();
        },
    });
</script>
</html>
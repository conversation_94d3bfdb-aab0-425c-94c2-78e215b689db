<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/elementui.css">
    <style>
        .time {
            font-size: 13px;
            color: #999;
        }

        .bottom {
            margin-top: 13px;
            line-height: 12px;
        }

        .button {
            padding: 0;
            float: right;
        }

        .image {
            width: 100%;
            height: 300px;
            display: block;
        }

        .clearfix:before,
        .clearfix:after {
            display: table;
            content: "";
        }

        .clearfix:after {
            clear: both
        }
        body {
            background-color: #fff;
        }
        #app {
            padding: 20px;
        }

        .header {
            margin-top: 30px;
        }

        .table {
            margin-top: 60px;
        }

        .next_button {
            padding-top: 60px;
            text-align: center;
        }

        .next_button button{
            width: 280px;
            font-size: 17px;
        }

        .next_button1 button{
            margin-top: 50px;
        }
        .page {
            margin-top: 30px;
            text-align: center;
        }

    </style>
</head>
<body>
<div id="app">

    <div class="bread">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item><a href="/">首页</a></el-breadcrumb-item>
            <el-breadcrumb-item><a href="/">模型训练</a></el-breadcrumb-item>
        </el-breadcrumb>
    </div>

    <div class="header">

        <el-steps :active="stepNum" simple>
            <el-step title="选择数据集" icon="el-icon-s-flag"></el-step>
            <el-step title="选择模型" icon="el-icon-s-order"></el-step>
            <el-step title="数据预处理与特征选择" icon="el-icon-s-data"></el-step>
            <el-step title="模型训练" icon="el-icon-s-data"></el-step>
            <el-step title="模型发布" icon="el-icon-finished"></el-step>
        </el-steps>

    </div>
    <div class="table">
        <div class="step1" v-show="stepNum==1">
            <div>
                <el-table
                        ref="multipleTable"
                        :data="tableData"
                        tooltip-effect="dark"
                        style="width: 100%"
                        @selection-change="handleSelectionChange"
                        max-height="550">
                    <el-table-column
                            type="selection"
                            width="100">
                    </el-table-column>
                    <el-table-column
                            prop="streamTime"
                            label="消费时间"
                            width="330">
                    </el-table-column>
                    <el-table-column
                            prop="streamMoney"
                            label="消费金额(元)"
                            width="380">
                    </el-table-column>
                    <el-table-column
                            prop="streamSignLocation"
                            label="注册地"
                            width="450"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="streamConsumeLocation"
                            label="消费地"
                    >
                    </el-table-column>
                </el-table>
            </div>
            <div class="next_button next_button2">
                <el-button type="success" @click="click1">提交至本地<i class="el-icon-arrow-right el-icon--right"></i></el-button>
                <el-button type="primary" @click="click1">提交至大数据平台<i class="el-icon-arrow-right el-icon--right"></i></el-button>
            </div>

        </div>
        <div class="step2" v-show="stepNum==2">
            <div>
                <el-col :span="5" :offset="0">
                    <el-card :body-style="{ padding: '0px' }">
                        <img src="images/K-近邻.png" class="image" >
                        <div style="padding: 14px;">
                            <span>K-近邻算法</span>
                            <div class="bottom clearfix">
                                <time class="time">新数据可以直接加入数据集</time>
                                <el-button type="text" class="button" size="medium">选择</el-button>
                            </div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="5" :offset="1">
                    <el-card :body-style="{ padding: '0px' }">
                        <img src="images/线性回归.png" class="image">
                        <div style="padding: 14px;">
                            <span>线性回归</span>
                            <div class="bottom clearfix">
                                <time class="time">新数据可以直接加入数据集</time>
                                <el-button type="text" class="button" size="medium">选择</el-button>
                            </div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="5" :offset="1">
                    <el-card :body-style="{ padding: '0px' }">
                        <img src="images/逻辑回归.png" class="image">
                        <div style="padding: 14px;">
                            <span>逻辑回归</span>
                            <div class="bottom clearfix">
                                <time class="time">新数据可以直接加入数据集</time>
                                <el-button type="text" class="button" size="medium">选择</el-button>
                            </div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="5" :offset="1">
                    <el-card :body-style="{ padding: '0px' }">
                        <img src="images/决策树.png" class="image">
                        <div style="padding: 14px;">
                            <span>决策树</span>
                            <div class="bottom clearfix">
                                <time class="time">新数据可以直接加入数据集</time>
                                <el-button type="text" class="button" size="medium">选择</el-button>
                            </div>
                        </div>
                    </el-card>
                </el-col>
            </div>

            <div class="next_button next_button1">
                <el-button type="primary" @click="click2">下一步<i class="el-icon-arrow-right el-icon--right"></i></el-button>
            </div>
        </div>
        <div class="step" v-show="stepNum==3">
            <el-button type="primary" @click="click3">下一步<i class="el-icon-arrow-right el-icon--right"></i></el-button>
        </div>
        <div class="step3" v-show="stepNum==4">
            <div>
                <el-table
                        :data="resultData"
                        stripe
                        style="width: 100%">
                    <el-table-column
                            type="selection"
                            width="100">
                    </el-table-column>
                    <el-table-column
                            prop="streamTime"
                            label="消费时间"
                            width="330">
                    </el-table-column>
                    <el-table-column
                            prop="streamMoney"
                            label="消费金额(元)"
                            width="380">
                    </el-table-column>
                    <el-table-column
                            prop="streamSignLocation"
                            label="注册地"
                            width="350"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="streamConsumeLocation"
                            label="消费地"
                            width="450"
                    >
                    </el-table-column>
                    <el-table-column
                            label="是否异常"
                    >
                        <template slot-scope="scope">
                            <el-tag type="success" v-if="scope.row.streamIsNormal==1">正常消费</el-tag>
                            <el-tag type="danger" v-if="scope.row.streamIsNormal==0">异常消费</el-tag>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <div class="page">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[100, 200, 300, 400]"
                        :page-size="100"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="400">
                </el-pagination>
            </div>

            <div class="next_button next_button1">
                <el-button type="primary" @click="click4">下一步<i class="el-icon-arrow-right el-icon--right"></i></el-button>
            </div>
        </div>

        <div class="step4" v-show="stepNum==5">
            <div>
                <el-table
                        :data="modelData"
                        stripe
                        style="width: 100%">
                    <el-table-column
                            type="selection"
                            width="100">
                    </el-table-column>
                    <el-table-column
                            prop="modelDate"
                            label="发布日期"
                            width="330">
                    </el-table-column>
                    <el-table-column
                            prop="modelName"
                            label="模型名称"
                            width="330">
                    </el-table-column>
                    <el-table-column
                            prop="modelInfo"
                            label="模型介绍"
                            width="480">
                    </el-table-column>
                    <el-table-column
                            prop="modelUser"
                            label="训练人"
                            width="380">
                    </el-table-column>
                    <el-table-column
                            prop="modelParameter"
                            label="训练参数"
                    >
                    </el-table-column>
                </el-table>
            </div>

            <div class="page">
                <el-pagination
                        @size-change="handleModelSizeChange"
                        @current-change="handleModelCurrentChange"
                        :current-page="modelPage"
                        :page-sizes="[100, 200, 300, 400]"
                        :page-size="100"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="400">
                </el-pagination>
            </div>

        </div>

    </div>



</div>
</body>
<script src="js/vue.js"></script>
<script src="js/axios.js"></script>
<script src="js/elementui.js"></script>
<script>
    new Vue({
        el: "#app",
        data() {
            return {
                tableData: [{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                }],
                resultData: [{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                }],
                modelData: [
                    {
                        modelDate: '2016-05-02 14:59',
                        modelName: 'K-近邻算法',
                        modelInfo: '新数据可以直接加入数据集',
                        modelUser: '王小虎',
                        modelParameter: 'K值=3'
                    },
                    {
                        modelDate: '2016-05-02 14:59',
                        modelName: '线性回归',
                        modelInfo: '新数据可以直接加入数据集',
                        modelUser: '王小虎',
                        modelParameter: 'K值=3'
                    },
                    {
                        modelDate: '2016-05-02 14:59',
                        modelName: '决策树',
                        modelInfo: '新数据可以直接加入数据集',
                        modelUser: '王小虎',
                        modelParameter: 'K值=3'
                    },
                    {
                        modelDate: '2016-05-02 14:59',
                        modelName: '逻辑回归',
                        modelInfo: '新数据可以直接加入数据集',
                        modelUser: '王小虎',
                        modelParameter: 'K值=3'
                    },
                    {
                        modelDate: '2016-05-02 14:59',
                        modelName: 'K-近邻算法',
                        modelInfo: '新数据可以直接加入数据集',
                        modelUser: '王小虎',
                        modelParameter: 'K值=3'
                    },
                    {
                        modelDate: '2016-05-02 14:59',
                        modelName: 'K-近邻算法',
                        modelInfo: '新数据可以直接加入数据集',
                        modelUser: '王小虎',
                        modelParameter: 'K值=3'
                    },
                    {
                        modelDate: '2016-05-02 14:59',
                        modelName: 'K-近邻算法',
                        modelInfo: '新数据可以直接加入数据集',
                        modelUser: '王小虎',
                        modelParameter: 'K值=3'
                    }
                ],
                currentPage: 1,
                modelPage: 1,
                condition: "",
                opt: "",
                multipleSelection: [],
                stepNum: 1
            }
        },
        methods: {
            goBack() {
                console.log('go back');
            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            click1() {
                this.stepNum=2;
            },
            click2() {
                this.stepNum=3;
            },
            click3() {
                this.stepNum=4;
            },
            click4() {
                this.stepNum=5;
            },
            handleSizeChange(val) {
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                console.log(`当前页: ${val}`);
            },
            handleModelSizeChange(val) {
                console.log(`每页 ${val} 条`);
            },
            handleModelCurrentChange(val) {
                console.log(`当前页: ${val}`);
            },
        },
    });
</script>
</html>
package com.lcc.localStreamData.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lcc.localStreamData.entity.Rule;
import com.lcc.localStreamData.entity.TestDataProcess;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface TestDataProcessService extends IService<TestDataProcess> {
    public void addDataByStrategy1();

    public void addDataByStrategy2();

    public void addDataByStrategy3();

    public void resetData();

    public Map overview();

    public List<Map<String, Object>> type();

    public List<Map<String, Object>> sign();

    public List<Map<String, Object>> consumeTop13(String date);

    public List<Map> nationTop(String date);

    public long[] line(String date, String time);

    public List<Map> hotProvince(String date);

    public List<Map> hotSale(String date, String place);
}

<template>
  <div class="risk-rule-training">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>风险评估规则/决策训练</span>
          <el-button type="primary" @click="startTraining" :loading="isTraining">
            {{ isTraining ? '训练中...' : '开始训练' }}
          </el-button>
        </div>
      </template>

      <!-- 规则配置 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="never">
            <template #header>
              <h4>规则配置</h4>
            </template>
            <div class="rule-builder">
              <div v-for="(rule, index) in rules" :key="index" class="rule-item">
                <el-card shadow="hover">
                  <div class="rule-header">
                    <span>规则 {{ index + 1 }}</span>
                    <el-button size="small" type="danger" @click="removeRule(index)">删除</el-button>
                  </div>
                  <el-form :model="rule" label-width="80px">
                    <el-form-item label="字段">
                      <el-select v-model="rule.field" placeholder="选择字段">
                        <el-option label="交易金额" value="amount" />
                        <el-option label="交易时间" value="time" />
                        <el-option label="交易地点" value="location" />
                        <el-option label="用户年龄" value="age" />
                        <el-option label="账户余额" value="balance" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="操作符">
                      <el-select v-model="rule.operator" placeholder="选择操作符">
                        <el-option label="大于" value=">" />
                        <el-option label="小于" value="<" />
                        <el-option label="等于" value="=" />
                        <el-option label="包含" value="contains" />
                        <el-option label="不等于" value="!=" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="阈值">
                      <el-input v-model="rule.threshold" placeholder="输入阈值" />
                    </el-form-item>
                    <el-form-item label="权重">
                      <el-slider v-model="rule.weight" :min="0" :max="100" show-input />
                    </el-form-item>
                  </el-form>
                </el-card>
              </div>
              <el-button @click="addRule" type="dashed" style="width: 100%; margin-top: 10px;">
                添加规则
              </el-button>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card shadow="never">
            <template #header>
              <h4>训练配置</h4>
            </template>
            <el-form :model="trainingConfig" label-width="120px">
              <el-form-item label="训练算法">
                <el-select v-model="trainingConfig.algorithm">
                  <el-option label="决策树" value="decision_tree" />
                  <el-option label="随机森林" value="random_forest" />
                  <el-option label="梯度提升" value="gradient_boost" />
                  <el-option label="逻辑回归" value="logistic_regression" />
                </el-select>
              </el-form-item>
              <el-form-item label="训练数据集">
                <el-select v-model="trainingConfig.dataset">
                  <el-option label="历史交易数据" value="historical" />
                  <el-option label="模拟数据" value="simulated" />
                  <el-option label="混合数据" value="mixed" />
                </el-select>
              </el-form-item>
              <el-form-item label="训练比例">
                <el-slider v-model="trainingConfig.trainRatio" :min="60" :max="90" show-input />
              </el-form-item>
              <el-form-item label="交叉验证">
                <el-input-number v-model="trainingConfig.crossValidation" :min="3" :max="10" />
              </el-form-item>
              <el-form-item label="最大迭代">
                <el-input-number v-model="trainingConfig.maxIterations" :min="100" :max="10000" :step="100" />
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>

      <!-- 训练进度 -->
      <div class="training-progress" v-if="isTraining || trainingResults.accuracy > 0">
        <el-card shadow="never">
          <template #header>
            <h4>训练进度</h4>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="progress-info">
                <el-progress 
                  :percentage="trainingProgress" 
                  :status="trainingProgress === 100 ? 'success' : 'active'"
                  :stroke-width="10"
                />
                <p style="margin-top: 10px;">当前进度: {{ trainingProgress }}%</p>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="training-metrics">
                <div class="metric-item">
                  <span class="metric-label">准确率:</span>
                  <span class="metric-value">{{ trainingResults.accuracy }}%</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">精确率:</span>
                  <span class="metric-value">{{ trainingResults.precision }}%</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">召回率:</span>
                  <span class="metric-value">{{ trainingResults.recall }}%</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">F1分数:</span>
                  <span class="metric-value">{{ trainingResults.f1Score }}%</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 规则测试 -->
      <div class="rule-testing">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <h4>规则测试</h4>
              <el-button @click="testRules" :disabled="rules.length === 0">测试规则</el-button>
            </div>
          </template>
          <el-table :data="testResults" style="width: 100%" max-height="300">
            <el-table-column prop="id" label="测试ID" width="80" />
            <el-table-column prop="amount" label="交易金额" width="100" />
            <el-table-column prop="location" label="交易地点" width="120" />
            <el-table-column prop="time" label="交易时间" width="180" />
            <el-table-column prop="riskScore" label="风险评分" width="100">
              <template #default="scope">
                <el-tag :type="getRiskType(scope.row.riskScore)">
                  {{ scope.row.riskScore }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="prediction" label="预测结果" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.prediction === '正常' ? 'success' : 'danger'">
                  {{ scope.row.prediction }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="confidence" label="置信度" width="100">
              <template #default="scope">
                {{ scope.row.confidence }}%
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <!-- 规则性能分析 -->
      <div class="performance-analysis">
        <el-card shadow="never">
          <template #header>
            <h4>规则性能分析</h4>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="performance-metric">
                <h5>检测率</h5>
                <el-progress 
                  type="circle" 
                  :percentage="performanceMetrics.detectionRate"
                  :color="getPerformanceColor(performanceMetrics.detectionRate)"
                />
              </div>
            </el-col>
            <el-col :span="8">
              <div class="performance-metric">
                <h5>误报率</h5>
                <el-progress 
                  type="circle" 
                  :percentage="performanceMetrics.falsePositiveRate"
                  :color="getFalsePositiveColor(performanceMetrics.falsePositiveRate)"
                />
              </div>
            </el-col>
            <el-col :span="8">
              <div class="performance-metric">
                <h5>响应时间</h5>
                <div class="response-time">
                  <span class="time-value">{{ performanceMetrics.responseTime }}</span>
                  <span class="time-unit">ms</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const isTraining = ref(false)
const trainingProgress = ref(0)

const rules = ref([
  {
    field: 'amount',
    operator: '>',
    threshold: '10000',
    weight: 80
  }
])

const trainingConfig = reactive({
  algorithm: 'random_forest',
  dataset: 'historical',
  trainRatio: 80,
  crossValidation: 5,
  maxIterations: 1000
})

const trainingResults = reactive({
  accuracy: 0,
  precision: 0,
  recall: 0,
  f1Score: 0
})

const testResults = ref([])

const performanceMetrics = reactive({
  detectionRate: 85,
  falsePositiveRate: 12,
  responseTime: 45
})

const addRule = () => {
  rules.value.push({
    field: '',
    operator: '',
    threshold: '',
    weight: 50
  })
}

const removeRule = (index) => {
  rules.value.splice(index, 1)
}

const startTraining = async () => {
  if (isTraining.value) return
  
  isTraining.value = true
  trainingProgress.value = 0
  
  ElMessage.info('开始训练风险评估模型...')
  
  // 模拟训练过程
  const trainingInterval = setInterval(() => {
    trainingProgress.value += Math.floor(Math.random() * 10) + 5
    
    if (trainingProgress.value >= 100) {
      trainingProgress.value = 100
      clearInterval(trainingInterval)
      isTraining.value = false
      
      // 设置训练结果
      trainingResults.accuracy = 92
      trainingResults.precision = 89
      trainingResults.recall = 94
      trainingResults.f1Score = 91
      
      ElMessage.success('模型训练完成！')
    }
  }, 500)
}

const testRules = () => {
  // 生成测试数据
  testResults.value = Array.from({ length: 10 }, (_, i) => {
    const riskScore = Math.floor(Math.random() * 100)
    return {
      id: i + 1,
      amount: Math.floor(Math.random() * 50000) + 1000,
      location: ['北京', '上海', '广州', '深圳', '杭州'][Math.floor(Math.random() * 5)],
      time: new Date().toLocaleString(),
      riskScore,
      prediction: riskScore > 70 ? '异常' : '正常',
      confidence: Math.floor(Math.random() * 30) + 70
    }
  })
  
  ElMessage.success('规则测试完成')
}

const getRiskType = (score) => {
  if (score > 80) return 'danger'
  if (score > 60) return 'warning'
  return 'success'
}

const getPerformanceColor = (percentage) => {
  if (percentage >= 80) return '#67C23A'
  if (percentage >= 60) return '#E6A23C'
  return '#F56C6C'
}

const getFalsePositiveColor = (percentage) => {
  if (percentage <= 10) return '#67C23A'
  if (percentage <= 20) return '#E6A23C'
  return '#F56C6C'
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rule-builder {
  max-height: 500px;
  overflow-y: auto;
}

.rule-item {
  margin-bottom: 15px;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-weight: bold;
}

.training-progress {
  margin: 20px 0;
}

.progress-info {
  text-align: center;
}

.training-metrics {
  padding: 20px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.metric-label {
  color: #666;
}

.metric-value {
  font-weight: bold;
  color: #409EFF;
}

.rule-testing {
  margin: 20px 0;
}

.performance-analysis {
  margin-top: 20px;
}

.performance-metric {
  text-align: center;
  padding: 20px;
}

.performance-metric h5 {
  margin-bottom: 20px;
  color: #333;
}

.response-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.time-value {
  font-size: 36px;
  font-weight: bold;
  color: #409EFF;
}

.time-unit {
  color: #666;
  margin-top: 5px;
}
</style>

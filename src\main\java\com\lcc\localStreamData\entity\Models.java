package com.lcc.localStreamData.entity;


import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class Models implements Serializable {
    @TableId(value = "number")
    private Long number;

    private LocalDateTime time;

    private Integer id;//模型所属类型编号

    private String modelName;

    private String modelDescription;

    private String trainer;

    private Integer trainer_id;

    private String modelType;

    private String trainingParameters;

}

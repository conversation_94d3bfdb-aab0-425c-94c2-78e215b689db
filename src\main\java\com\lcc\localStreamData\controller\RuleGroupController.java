package com.lcc.localStreamData.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcc.localStreamData.common.R;
import com.lcc.localStreamData.entity.Rule;
import com.lcc.localStreamData.entity.RuleGroup;
import com.lcc.localStreamData.entity.dto.RuleGroupDto;
import com.lcc.localStreamData.entity.vo.RuleGroupVo;
import com.lcc.localStreamData.service.RuleGroupService;
import com.lcc.localStreamData.service.RuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/ruleGroup")
public class RuleGroupController {
    @Autowired
    RuleGroupService ruleGroupService;
    @Autowired
    RuleService ruleService;

    @GetMapping("/list")
    public R<Page<RuleGroupVo>> list(int page, int pageSize, String name) {
        Page<RuleGroupVo> list = ruleGroupService.findPage(page, pageSize, name);
        return R.success(list);
    }

    @GetMapping("/all")
    public R<List<RuleGroup>> getAll() {
        List<RuleGroup> list = ruleGroupService.list();
        return R.success(list);
    }

    @PostMapping
    public R<String> save(@RequestBody RuleGroupDto ruleGroupDto) {
        ruleGroupService.saveRuleGroup(ruleGroupDto.getName(), ruleGroupDto.getRuleList());
        return R.success("保存成功");
    }

    @PutMapping("/{id}")
    public R<String> updateRuleGroup(@RequestBody RuleGroupDto ruleGroupDto) {
        List<Rule> ruleListToUpdate = ruleGroupDto.getRuleList()
                .stream().filter(rule -> rule.getId() != null).collect(Collectors.toList());
        List<Rule> RuleListToSave = ruleGroupDto.getRuleList()
                .stream().filter(rule -> rule.getId() == null).collect(Collectors.toList());
        //删除冗余规则
        List<Integer> IdListToUpdate = ruleListToUpdate
                .stream().map(Rule::getId).collect(Collectors.toList());
        ruleService.removeOldRules(ruleGroupDto.getId(), IdListToUpdate);
        //更新已存在的规则
        ruleService.updateBatchById(ruleListToUpdate);
        //添加新规则
        RuleListToSave.forEach(rule -> rule.setRuleGroupId(ruleGroupDto.getId()));
        ruleService.saveBatch(RuleListToSave);
        RuleGroup ruleGroup = new RuleGroup();
        BeanUtils.copyProperties(ruleGroupDto, ruleGroup);
        ruleGroupService.updateById(ruleGroup);
        return R.success("修改成功");
    }

    @GetMapping("/{id}")
    public R<RuleGroupVo> getRuleGroup(@PathVariable Integer id) {
        RuleGroupVo ruleGroupVo = ruleGroupService.getRuleGroup(id);
        return R.success(ruleGroupVo);
    }

    @DeleteMapping("/{id}")
    public R<String> removeRuleGroup(@PathVariable Integer id) {
        ruleGroupService.removeRuleGroup(id);
        return R.success("删除成功");
    }
}

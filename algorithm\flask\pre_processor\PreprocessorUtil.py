import os
from pathlib import Path
from module_loader.module_loader import get_class_definition


processor_map=dict()# k:module_name, v:module_path
# 模块被加载时执行：
# processor_map只能在模块被加载时进行写操作(线程不安全)
current_dir=os.path.dirname(os.path.abspath(__file__))
for dir_name in os.listdir(current_dir):# 遍历子目录
    sub_dir = Path(os.path.join(current_dir, dir_name)).resolve()
    for path in sub_dir.rglob('*.py'):# 找子目录里的所有.py文件
        # print(path.relative_to(current_dir))
        relative_path = path.relative_to(current_dir)
        if relative_path.name == '__init__.py':#  忽略__init__.py
            continue
        module_path = relative_path.with_suffix('')
        module_path = __package__+"."+str(module_path).replace(os.sep, '.') # 模块路径
        module_name=module_path.split('.')[-1] #  模块名
        processor_map[module_name]=module_path
# print("processor_map:",processor_map)

def build_process_chain(preprocessor_configs: list):
    if len(preprocessor_configs) == 0:
        return None
    
    # 获取第一个处理器的模块路径和类定义
    module_name = preprocessor_configs[0]['name']
    module_path = processor_map[module_name]
    class_def = get_class_definition(module_path, module_name)
    
    # 提取构造参数并实例化
    args = preprocessor_configs[0].get('args', {})  # 获取构造参数，默认为空字典
    p = class_def(**args)  # 动态实例化类对象
    
    # 构建链式处理器
    for i in range(1, len(preprocessor_configs)):
        config = preprocessor_configs[i]
        module_path = processor_map[config['name']]
        class_def = get_class_definition(module_path, config['name'])
        
        # 提取构造参数并实例化
        args = config.get('args', {})  # 获取构造参数，默认为空字典
        
        # 设置链式关系
        p.set_next(class_def(**args))
        p = p.get_next()
    
    return p  # 返回链式处理器的头节点

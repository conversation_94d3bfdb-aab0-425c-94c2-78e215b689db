import importlib
import inspect

def get_class_definition(module_path: str, class_name: str):
    """
    根据模块路径和类名，返回对应的类定义
    
    参数:
    module_path (str): 包含目标类的模块路径。
    class_name (str): 目标类的名称。
    
    返回:
    type: 目标类的定义，如果找不到类或发生错误则返回 None。
    """
    try:
        # 动态导入模块
        module = importlib.import_module(module_path)
        for name, cls in inspect.getmembers(module, inspect.isclass):
            if name.lower() == class_name.lower():
                return cls  # 返回类定义
    except ImportError as e:
        print(f"错误：无法加载模块 '{module_path}'. 请检查路径和文件名。错误信息: {e}")
        return None
    except Exception as e:
        print(f"发生未知错误: {e}")
        return None
            
    return None  # 如果没有找到匹配的类，返回 None
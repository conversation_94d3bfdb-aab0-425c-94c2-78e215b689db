<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>金融在线分析系统</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        .el-header {
            background-color: #B3C0D1;
            color: #333;
            line-height: 60px;
        }

        .el-aside {
            color: #333;
        }

        .el-main {
            padding: 10px !important;
            background-color: #f3f4f7;
        }

        html {
            height: 100%;
        }

        body {
            height: 100%;
        }

    </style>
</head>
<body>
<div id="app" style="height: 100%">
    <div style="height: 96%;">
        <el-container style="height: 100%">
            <el-aside width="200px" style="background-color: #061031;height: 100%">
                <img src="images/logo1.jpg" alt="" style="width: 100%;margin-bottom: 10px">
                <el-menu
                        default-active="2"
                        class="el-menu-vertical-demo"
                        @open="handleOpen"
                        @close="handleClose"
                        background-color="#061031"
                        text-color="#fff"
                        active-text-color="#ffd04b">
                    <el-menu-item index="1">
                        <i class="el-icon-menu"></i>
                        <span slot="title">首页</span>
                    </el-menu-item>
                    <el-menu-item index="2" @click="iframeUrl='panel.html'">
                        <i class="el-icon-menu"></i>
                        <span slot="title">Dashboard</span>
                    </el-menu-item>
                    <el-menu-item index="3" @click="iframeUrl='user.html'" v-if="userInfo.userLevel=='1'">
                        <i class="el-icon-s-custom"></i>
                        <span slot="title">用户管理</span>
                    </el-menu-item>
                    <!--                    <el-menu-item index="4" @click="iframeUrl='data.html'">-->
                    <!--                        <i class="el-icon-s-data"></i>-->
                    <!--                        <span slot="title">数据管理</span>-->
                    <!--                    </el-menu-item>-->
                    <el-submenu index="4">
                        <template slot="title">
                            <i class="el-icon-s-data"></i>
                            <span slot="title">数据管理&预处理</span>
                        </template>
                        <el-menu-item-group>
                            <el-menu-item index="4-1" @click="iframeUrl='data.html'">
                                <!--                                <i class="el-icon-s-data"></i>-->
                                <span slot="title">流数据管理</span>
                            </el-menu-item>
                            <el-menu-item index="4-2" @click="iframeUrl='dataset.html'">
                                <span slot="title">数据集管理</span>
                            </el-menu-item>
                            <el-menu-item index="4-3" @click="iframeUrl='process.html'">
                                <span slot="title">数据预处理</span>
                            </el-menu-item>
                        </el-menu-item-group>
                    </el-submenu>
                    <el-submenu index="5">
                        <template slot="title">
                            <i class="el-icon-setting"></i>
                            <span slot="title">模型管理</span>
                        </template>
                        <el-menu-item-group>
                            <el-menu-item index="5-1" @click="iframeUrl='rule.html'">
                                <span slot="title">规则管理</span>
                            </el-menu-item>
                            <el-menu-item index="5-2" @click="iframeUrl='model-admin.html'"
                                          v-if="userInfo.userLevel=='1'">
                                <span slot="title">模型训练</span>
                            </el-menu-item>
                            <el-menu-item index="5-2" @click="iframeUrl='model-user.html'" v-else>
                                <span slot="title">模型训练</span>
                            </el-menu-item>
                        </el-menu-item-group>
                    </el-submenu>
                    <el-submenu index="6">
                        <template slot="title">
                            <i class="el-icon-warning"></i>
                            <span slot="title">风险评估</span>
                        </template>
                        <el-menu-item-group>
                            <el-menu-item index="6-1" @click="iframeUrl='risk-rule.html'">
                                <span slot="title">规则评估</span>
                            </el-menu-item>
                            <el-menu-item index="6-2" @click="iframeUrl='risk-model.html'">
                                <span slot="title">平台模型评估</span>
                            </el-menu-item>
                            <el-menu-item index="6-3" @click="iframeUrl='risk-selfmodel.html'">
                                <span slot="title">自训练模型评估</span>
                            </el-menu-item>
                            <el-menu-item index="6-4" @click="iframeUrl='risk-fusion.html'">
                                <span slot="title">规则+模型融合评估</span>
                            </el-menu-item>
                        </el-menu-item-group>
                    </el-submenu>
                    <!--<el-menu-item index="7" @click="iframeUrl='risk.html'">
                        <i class="el-icon-warning"></i>
                        <span slot="title">风险评估</span>
                    </el-menu-item>-->
                    <el-menu-item index="8" @click="iframeUrl='user-paint.html'">
                        <i class="el-icon-phone-outline"></i>
                        <span slot="title">用户预警</span>
                    </el-menu-item>
                    <el-submenu index="9">
                        <template slot="title">
                            <i class="el-icon-location"></i>
                            <span>平台监控</span>
                        </template>
                        <el-menu-item-group>
                            <el-menu-item index="9-1">
                                <a style="color: #fff" href="http://10.16.9.145:9870" target="_blank">HDFS</a>
                            </el-menu-item>
                            <el-menu-item index="9-2">
                                <a style="color: #fff" href="http://10.16.30.215:8088" target="_blank">YARN</a>
                            </el-menu-item>
                            <el-menu-item index="9-3">
                                <a style="color: #fff" href="http://10.16.9.145:19888" target="_blank">MapReduce</a>
                            </el-menu-item>
                        </el-menu-item-group>
                    </el-submenu>
                </el-menu>
            </el-aside>

            <el-container>

                <el-header style="font-size: 12px; background-color:#061031;color: white;">

                    <el-row :gutter="20">
                        <el-col :span="16">
                            <div style="font-size: 24px;font-weight: bold;margin-left: 50%;letter-spacing: 3px;">
                                面向金融行业流数据的在线分析系统
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <div style="text-align: right">
                                <el-dropdown>
                                    <i class="el-icon-setting" style="margin-right: 15px"></i>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item><span @click="logout">注销</span></el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                                <span>{{ userInfo.username }}</span>
                            </div>
                        </el-col>
                    </el-row>


                </el-header>

                <el-main>

                    <iframe :src="iframeUrl" frameborder="0" width="100%" height="100%"></iframe>

                </el-main>

            </el-container>
        </el-container>
    </div>

    <div style="background-color:#acabad;height: 4%;text-align: center;color: #2a2831;font-size: 16px;font-weight: bold;line-height: 40px">
        重庆邮电大学 大数据智能计算创新研发团队@2015-2023星环信息科技(上海)股份有限公司&重庆邮电大学联合出品
    </div>

</div>
</body>
<script src="js/vue.js"></script>
<script src="js/axios.js"></script>
<script src="js/elementui.js"></script>
<script>
    let app = new Vue({
        el: "#app",
        data() {
            return {
                iframeUrl: "panel.html",
                userInfo: {}
            }
        },
        methods: {
            logout() {

                axios.post(`/logout`).then(response => {
                    if (response.data.code === 1) {
                        localStorage.removeItem('userInfo')
                        window.location.href = '/login.html'
                    }
                })

            }
        },
        created() {
            const userInfo = window.localStorage.getItem('userInfo')
            if (userInfo) {
                this.userInfo = JSON.parse(userInfo)
            }
        }
    });
</script>
</html>
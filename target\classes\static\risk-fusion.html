<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/elementui.css">
    <style>
        body {
            background-color: #fff;
        }

        #app {
            padding: 20px;
        }

        .form {
            width: 1200px;
            margin: 55px auto;
        }

        .ruleGroupOption {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

    </style>
</head>
<body>
<div id="app">

    <div class="bread">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item><a href="/">首页</a></el-breadcrumb-item>
            <el-breadcrumb-item><a href="/">融合评估</a></el-breadcrumb-item>
        </el-breadcrumb>
    </div>

    <div class="form">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" class="demo-ruleForm">
            <el-form-item label="评估规则" prop="ruleGroupIds">
                <el-select v-model="ruleForm.ruleGroupIds" placeholder="请选择评估的规则" style="width: 400px">
                    <el-option class="ruleGroupOption" v-for="item in ruleGroupList"
                               :key="item.id" :label="item.name" :value="item.id">
                        <span>{{item.name}}</span>
                        <el-button type="text" @click="showRuleDialog(item.id)">查看详情</el-button>
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="评估模型" prop="modelIds">
                <el-select v-model="ruleForm.modelIds" placeholder="请选择评估的模型" style="width: 400px">
                    <el-option v-for="item in modelList"
                               :key="item.value" :label="item.label" :value="item.value">
                        {{item.label}}
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="请选择评估数据集">
                <el-table
                        ref="multipleTable"
                        :data="tableData"
                        tooltip-effect="dark"
                        style="width: 100%"
                        @selection-change="handleSelectionChange"
                        max-height="550">
                    <el-table-column
                            type="selection"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            prop="streamTime"
                            label="消费时间"
                            width="280">
                    </el-table-column>
                    <el-table-column
                            prop="streamMoney"
                            label="消费金额(元)"
                            width="280">
                    </el-table-column>
                    <el-table-column
                            prop="streamSignLocation"
                            label="注册地"
                            width="280"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="streamConsumeLocation"
                            label="消费地"
                    >
                    </el-table-column>
                </el-table>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="submitForm('ruleForm')">立即评估</el-button>
                <el-button @click="resetForm('ruleForm')">重置</el-button>
            </el-form-item>
        </el-form>
    </div>

    <!-- 规则组详情对话框 -->
    <el-dialog title="规则组详情" :visible.sync="dialogVisible" width="700px">
        <div style="font-weight: bold;padding-bottom: 10px;">{{ruleGroupInfo.name}}</div>
        <div style="padding-bottom: 10px;">创建时间：{{ruleGroupInfo.createTime}}</div>
        <!--<div style="font-weight: bold">创建人：{{ruleGroupInfo.createUser}}</div>-->
        <el-table
                :data="ruleGroupInfo.ruleList"
                stripe
                style="width: 100%">
            <el-table-column
                    type="index"
                    :index="detailIndexMethod"
                    width="100">
            </el-table-column>
            <el-table-column
                    label="规则详情"
                    width="500">
                <template slot-scope="scope">
                    <span v-if="scope.row.type=='1'">
                        {{scope.row.timeLimit}}秒内消费{{scope.row.freqLimit}}次则视为异常
                    </span>
                    <span v-else-if="scope.row.type=='2'">
                        {{scope.row.timeLimit}}秒内在不同城市进行过消费则视为异常
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false;">取 消</el-button>
        </div>
    </el-dialog>

    <div
            v-loading.fullscreen.lock="fullscreenLoading"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
    >
    </div>

</div>
</body>
<script src="js/vue.js"></script>
<script src="js/axios.js"></script>
<script src="js/elementui.js"></script>
<script src="js/moment.js"></script>
<script src="js/jquery.min.js"></script>
<script>
    new Vue({
        el: "#app",
        data() {
            return {
                fullscreenLoading: false,
                tableData: [],
                ruleGroupList: [],
                modelList: [],  // 用于存储模型列表
                dialogVisible: false,
                ruleGroupInfo: {
                    id: null,
                    name: "",
                    createUser: null,
                    createTime: "",
                    ruleList: []
                },
                dataIds: [],
                ruleForm: {
                    ruleGroupIds: '',   // 评估规则
                    modelIds: '',  // 评估模型
                    dataIds: '',  // 评估数据
                },
                rules: {
                    ruleGroupIds: [
                        {required: true, message: '请选择评估规则', trigger: 'change'}
                    ],
                    modelIds: [
                        {required: true, message: '请选择评估模型', trigger: 'change'}
                    ],
                }
            }
        },
        methods: {
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid && this.dataIds.length > 0) {
                        for (let i = 0; i < this.dataIds.length; i++) {
                            if ((i + 1) != this.dataIds.length) {
                                this.ruleForm.dataIds += (this.dataIds[i].streamId + ",")
                            } else {
                                this.ruleForm.dataIds += (this.dataIds[i].streamId + "")
                            }
                        }
                        this.ruleForm.modelIds = this.ruleForm.modelIds.toString()
                        this.ruleForm.ruleGroupIds = this.ruleForm.ruleGroupIds.toString()
                        var that = this;
                        this.fullscreenLoading = true;
                        console.log(this.ruleForm)
                        axios.post(`/fusion/test`, this.ruleForm).then(response => {
                            localStorage.setItem("dataIds", that.ruleForm.dataIds)
                            location.href = "risk-result.html"
                            that.fullscreenLoading = false;
                        });
                        console.log(this.ruleForm);
                    } else {
                        alert("请完善表单")
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            resetForm(formName) {
                this.$refs[formName].resetFields();
            },
            handleSelectionChange(val) {
                console.log(val);
                this.dataIds = val;
            },
            getList() {
                axios.get(`/risk/list`).then(response => {
                    let data = response.data.data
                    //格式转换
                    for (let i = 0; i < data.length; i++) {
                        data[i].streamTime = moment(data[i].streamTime).format('YYYY-MM-DD HH:mm:ss');
                        data[i].streamConsumeLocation = data[i].streamConsumeLocation.replaceAll(",", "-")
                    }
                    this.tableData = data
                })
            },
            getRuleGroupList() {
                axios.get(`/ruleGroup/all`).then(response => {
                    if (response.data.code == 1) {
                        this.ruleGroupList = response.data.data
                    } else {
                        this.$message.error(response.data.msg)
                    }
                })
            },
            getModelList() {
                const adminName = "admin"
                axios.get(`/models/list/admin?page=${1}&pageSize=${1000}&trainer=${adminName}`).then(response => { // 模型接口
                    if (response.data.code == 1) {
                        this.modelList = response.data.data.records.map(model => ({
                            value: model.number,
                            label: model.modelName
                        }));
                    } else {
                        this.$message.error(response.data.msg)
                    }
                })
            },
            showRuleDialog(id) {
                axios.get(`/ruleGroup/${id}`).then(response => {
                    if (response.data.code == 1) {
                        let row = response.data.data
                        this.ruleGroupInfo.name = row.name;
                        this.ruleGroupInfo.createUser = row.createUser;
                        this.ruleGroupInfo.createTime = moment(row.createTime).format("YYYY-MM-DD HH:mm:ss");
                        this.ruleGroupInfo.ruleList = row.ruleList;
                        this.dialogVisible = true;
                    } else {
                        this.$message.error(response.data.msg)
                    }
                })
            },
            detailIndexMethod(index) {
                return index + 1;
            },
        },
        mounted() {
            this.getList();
            this.getRuleGroupList(); // 获取规则组列表
            this.getModelList(); // 获取模型列表
        }
    });
</script>
</html>

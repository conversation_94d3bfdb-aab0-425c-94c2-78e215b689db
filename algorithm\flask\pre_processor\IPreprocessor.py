from abc import ABC, abstractmethod

import pandas as pd


class DataFramePreprocessor(ABC):
    _next_processor = None
    
    @abstractmethod
    def do_process(self, df:pd.DataFrame)->pd.DataFrame: # 处理逻辑
        pass
    @abstractmethod
    def use_in_predict(self)->bool: # 是否在预测时使用
        pass

    def set_next(self, processor):
        self._next_processor = processor
        return processor  # 返回下一个处理器便于链式调用

    def process(self, df):
        # 当前处理器处理数据
        processed_df = self.do_process(df)
        
        # 传递给下一个处理器
        if self._next_processor:
            return self._next_processor.process(processed_df)
        return processed_df

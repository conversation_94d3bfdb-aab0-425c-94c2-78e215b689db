package com.lcc.localStreamData.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lcc.localStreamData.entity.Models;
import com.lcc.localStreamData.mapper.ModelsMapper;
import com.lcc.localStreamData.service.ModelsService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;


@Service
public class ModelsServiceImpl extends ServiceImpl<ModelsMapper, Models> implements ModelsService {


    @Override
    public Page<Models> findListAdmin(int page, int pageSize, String trainer) {

        //构造分页构造器
        Page pageInfo = new Page(page, pageSize);
        //构造条件构造器
        QueryWrapper<Models> queryWrapper = new QueryWrapper<>();
        //添加过滤条件
        if (StringUtils.isNotEmpty(trainer)){
            queryWrapper.like("trainer",trainer);
        }
        //添加排序条件
        queryWrapper.orderByDesc("time");

        //执行查询
        Page result = this.page(pageInfo, queryWrapper);

        return result;
    }

    @Override
    public Page<Models> findListUser(int page, int pageSize, Integer trainerId) {

        //构造分页构造器
        Page pageInfo = new Page(page, pageSize);
        //构造条件构造器
        QueryWrapper<Models> queryWrapper = new QueryWrapper<>();
        //添加过滤条件
        queryWrapper.eq("trainer_id",trainerId);
        //添加排序条件
        queryWrapper.orderByDesc("time");
        //执行查询
        Page result = this.page(pageInfo, queryWrapper);

        return result;
    }
}

#server:
#  port: 8080
#spring:
#  application:
#    name: local_stream_data
#  datasource:
#    druid:
#      driver-class-name: com.mysql.cj.jdbc.Driver
#      url: *********************************************************************************************************************************************************************************************
#      username: root
#      password: 1234
#mybatis-plus:
#  configuration:
#    #在映射实体或者属性时，将数据库中表名和字段名中的下划线去掉，按照驼峰命名法映射
#    map-underscore-to-camel-case: true
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#  global-config:
#    db-config:
#      id-type: ASSIGN_ID
#feign-client: #feign远程调用配置
#  url:
#    big-data: ************:10408


server:
    port: 8080
spring:
    application:
      name: local_stream_data
    datasource:
      druid:
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ************************************************************************************************************************************************************************************
        username: root
        password: WinDocker123!
        validation-query: SELECT 1
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: ASSIGN_ID
feign-client:
  url:
    big-data: ************:10408
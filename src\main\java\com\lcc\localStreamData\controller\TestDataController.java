package com.lcc.localStreamData.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lcc.localStreamData.common.R;
import com.lcc.localStreamData.entity.TestData;
import com.lcc.localStreamData.service.TestDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/testData")
public class TestDataController {

    @Autowired
    private TestDataService testDataService;

    @GetMapping("/list")
    public R<List<TestData>> list(TestData category){
        List<TestData> list = testDataService.list();
        return R.success(list);

    }

    @PostMapping("/save")
    public R<String> save(@RequestBody TestData testData){
        testDataService.save(testData);
        return R.success("操作成功");
    }



}

package com.lcc.localStreamData.controller;

import com.lcc.localStreamData.common.R;
import com.lcc.localStreamData.entity.TestData;
import com.lcc.localStreamData.entity.TestDataProcess;
import com.lcc.localStreamData.service.TestDataProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/testDataProcess")
public class TestDataProcessController {

    @Autowired
    private TestDataProcessService testDataProcessService;

    @GetMapping("/list")
    public R<List<TestDataProcess>> list(TestData category){
        List<TestDataProcess> list = testDataProcessService.list();
        return R.success(list);
    }

    @PostMapping("/save1")
    public R<String> save1(){
        testDataProcessService.addDataByStrategy1();
        return R.success("操作成功");
    }

    @PostMapping("/save2")
    public R<String> save2(){
        testDataProcessService.addDataByStrategy2();
        return R.success("操作成功");
    }

    @PostMapping("/save3")
    public R<String> save3(){
        testDataProcessService.addDataByStrategy3();
        return R.success("操作成功");
    }



}

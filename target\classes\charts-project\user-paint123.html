<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/elementui.css">
    <script src="js/echarts.min.js"></script>
    <script src="js/jquery.min.js"></script>
    <script src="js/china.js"></script>
    <style>
        #app {
            padding: 20px;
        }
        .page {
            margin-top: 30px;
            text-align: center;
        }
        .header {

        }
        .header .search {
            width: 230px;
        }
        .table {
            border: 2px solid #f3f4f7;
            margin-top: 20px;

        }

        .left {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 3px 3px 2px #e7e7e7;
            padding-top: 20px;
            padding-bottom: 20px;
            height: 800px;
        }

        .right {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 3px 3px 2px #e7e7e7;
            padding-top: 20px;
            padding-bottom: 20px;
            height: 800px;
        }

        .bread {
            margin-bottom: 20px;
            background-color: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 3px 3px 2px #e7e7e7;
        }

        .right .title {
            font-size: 25px;
            font-weight: bold;
            border-bottom: 1px solid #303133;
            padding-bottom: 20px;
            color: #303133;
            text-align: center;
        }

        .switch {
            margin-top: 8px;
        }

        #chart1 {
            margin-top: 80px;
            width: 100%;
            height: 600px;
        }
        #chart2 {
            margin-top: 80px;
            width: 100%;
            height: 600px;
        }
    </style>
</head>
<body>
<div id="app">

    <div class="bread">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item><a href="/">首页</a></el-breadcrumb-item>
            <el-breadcrumb-item><a href="/">用户预警</a></el-breadcrumb-item>
        </el-breadcrumb>
    </div>

    <el-row :gutter="20">
        <el-col :span="8">
            <div class="left">
                <div class="table">
                    <el-table
                            :data="tableData"
                            stripe
                            style="width: 100%">
                        <el-table-column
                                type="index"
                                width="100">
                        </el-table-column>
                        <el-table-column
                                prop="streamUserId"
                                label="用户ID"
                                width="200">
                        </el-table-column>
                        <el-table-column
                                prop="streamSignLocation"
                                label="注册地"
                                width="200">
                        </el-table-column>
                        <el-table-column label="操作">
                            <template slot-scope="scope">
                                <el-button type="primary" icon="el-icon-search" plain>用户分析</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="page">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[100, 200, 300, 400]"
                            :page-size="100"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="400">
                    </el-pagination>
                </div>
            </div>
        </el-col>
        <el-col :span="16">
            <div class="right">
                <div class="title">用户分析结果</div>
                <div style="background-image:url(images/bg.jpg);">
                    <el-row :gutter="20">
                        <el-col :span="16">
                            <div id="chart1">
                                <dv-border-box-1>
                                    123
                                </dv-border-box-1>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <div id="chart2">



                            </div>
                        </el-col>
                    </el-row>
                </div>


            </div>
        </el-col>
    </el-row>




</div>
</body>
<script src="js/vue.js"></script>
<script src="js/axios.js"></script>
<script src="js/elementui.js"></script>
<!--调试版-->
<script src="https://unpkg.com/@jiaminghi/data-view/dist/datav.map.vue.js"></script>
<script>
    new Vue({
        el: "#app",
        data() {
            return {
                tableData: [{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                }],
                currentPage: 1,
                condition: "",
                opt: "",
                config: {}
            }
        },
        methods: {
            handleSizeChange(val) {
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                console.log(`当前页: ${val}`);
            },
            goBack() {
                console.log('go back');
            },
            refreshMap() {
                var myChart = echarts.init(document.querySelector("#chart1"));
                // 秋雁南飞：
// 此版本通过设置geoindex && seriesIndex: [1] 属性来实现geo和map共存，来达到hover散点和区域显示tooltip的效果
// 默认情况下，map series 会自己生成内部专用的 geo 组件。但是也可以用这个 geoIndex 指定一个 geo 组件。这样的话，map 和 其他 series（例如散点图）就可以共享一个 geo 组件了。并且，geo 组件的颜色也可以被这个 map series 控制，从而用 visualMap 来更改。
// 当设定了 geoIndex 后，series-map.map 属性，以及 series-map.itemStyle 等样式配置不再起作用，而是采用 geo 中的相应属性。
// http://echarts.baidu.com/option.html#series-map.geoIndex
// 并且加了pin气泡图标以示数值大小
// // 全局变量区:参考江西绿色金融（谢谢：本来想用闭包实现接口数据调用，没时间了）

// 本图作者：参考秋雁南飞的《投票统计》一图，网址：http://gallery.echartsjs.com/editor.html?c=xrJU-aE-LG
                var name_title = "中国人民大学2017年各省市计划录取人数"
                var subname = '数据爬取自千栀网\n，\n上海、浙江无文理科录取人数'
                var nameColor = " rgb(55, 75, 113)"
                var name_fontFamily = '等线'
                var subname_fontSize = 15
                var name_fontSize = 18
                var mapName = 'china'
                var data = [
                    {name:"北京",value:177},
                    {name:"天津",value:42},
                    {name:"河北",value:102},
                    {name:"山西",value:81},
                    {name:"内蒙古",value:47},
                    {name:"辽宁",value:67},
                    {name:"吉林",value:82},
                    {name:"黑龙江",value:66},
                    {name:"上海",value:24},
                    {name:"江苏",value:92},
                    {name:"浙江",value:114},
                    {name:"安徽",value:109},
                    {name:"福建",value:116},
                    {name:"江西",value:91},
                    {name:"山东",value:119},
                    {name:"河南",value:137},
                    {name:"湖北",value:116},
                    {name:"湖南",value:114},
                    {name:"重庆",value:91},
                    {name:"四川",value:125},
                    {name:"贵州",value:62},
                    {name:"云南",value:83},
                    {name:"西藏",value:9},
                    {name:"陕西",value:80},
                    {name:"甘肃",value:56},
                    {name:"青海",value:10},
                    {name:"宁夏",value:18},
                    {name:"新疆",value:67},
                    {name:"广东",value:123},
                    {name:"广西",value:59},
                    {name:"海南",value:14},
                ];

                var geoCoordMap = {};

                /*获取地图数据*/
                myChart.showLoading();
                var mapFeatures = echarts.getMap(mapName).geoJson.features;
                myChart.hideLoading();
                mapFeatures.forEach(function(v) {
                    // 地区名称
                    var name = v.properties.name;
                    // 地区经纬度
                    geoCoordMap[name] = v.properties.cp;

                });

                var max = 480,
                    min = 9; // todo
                var maxSize4Pin = 100,
                    minSize4Pin = 20;

                var convertData = function(data) {
                    var res = [];
                    for (var i = 0; i < data.length; i++) {
                        var geoCoord = geoCoordMap[data[i].name];
                        if (geoCoord) {
                            res.push({
                                name: data[i].name,
                                value: geoCoord.concat(data[i].value),
                            });
                        }
                    }
                    return res;
                };
                option = {
                    title: {
                        text: name_title,
                        subtext: subname,
                        x: 'center',
                        textStyle: {
                            color: nameColor,
                            fontFamily: name_fontFamily,
                            fontSize: name_fontSize
                        },
                        subtextStyle:{
                            fontSize:subname_fontSize,
                            fontFamily:name_fontFamily
                        }
                    },
                    geo: {
                        show: true,
                        map: mapName,
                        label: {
                            normal: {
                                show: false
                            },
                            emphasis: {
                                show: false,
                            }
                        },
                        roam: true,
                        itemStyle: {
                            normal: {
                                areaColor: '#031525',
                                borderColor: '#3B5077',
                            },
                            emphasis: {
                                areaColor: '#2B91B7',
                            }
                        }
                    },
                    series: [{
                        name: '散点',
                        type: 'scatter',
                        coordinateSystem: 'geo',
                        data: convertData(data),
                        symbolSize: function(val) {
                            return val[2] / 10;
                        },
                        label: {
                            normal: {
                                formatter: '{b}',
                                position: 'right',
                                show: true
                            },
                            emphasis: {
                                show: true
                            }
                        },
                        itemStyle: {
                            normal: {
                                color: '#05C3F9'
                            }
                        }
                    },
                        {
                            type: 'map',
                            map: mapName,
                            geoIndex: 0,
                            aspectScale: 0.75, //长宽比
                            showLegendSymbol: false, // 存在legend时显示
                            label: {
                                normal: {
                                    show: true
                                },
                                emphasis: {
                                    show: false,
                                    textStyle: {
                                        color: '#fff'
                                    }
                                }
                            },
                            roam: true,
                            itemStyle: {
                                normal: {
                                    areaColor: '#031525',
                                    borderColor: '#3B5077',
                                },
                                emphasis: {
                                    areaColor: '#2B91B7'
                                }
                            },
                            animation: false,
                            data: data
                        },
                        {
                            name: '点',
                            type: 'scatter',
                            coordinateSystem: 'geo',
                            symbol: 'pin', //气泡
                            symbolSize: function(val) {
                                var a = (maxSize4Pin - minSize4Pin) / (max - min);
                                var b = minSize4Pin - a * min;
                                b = maxSize4Pin - a * max;
                                return a * val[2] + b;
                            },
                            label: {
                                normal: {
                                    show: true,
                                    textStyle: {
                                        color: '#fff',
                                        fontSize: 9,
                                    }
                                }
                            },
                            itemStyle: {
                                normal: {
                                    color: '#F62157', //标志颜色
                                }
                            },
                            zlevel: 6,
                            data: convertData(data),
                        },
                        {
                            name: 'Top 5',
                            type: 'effectScatter',
                            coordinateSystem: 'geo',
                            data: convertData(data.sort(function(a, b) {
                                return b.value - a.value;
                            }).slice(0, 5)),
                            symbolSize: function(val) {
                                return val[2] / 10;
                            },
                            showEffectOn: 'render',
                            rippleEffect: {
                                brushType: 'stroke'
                            },
                            hoverAnimation: true,
                            label: {
                                normal: {
                                    formatter: '{b}',
                                    position: 'right',
                                    show: true
                                }
                            },
                            itemStyle: {
                                normal: {
                                    color: 'yellow',
                                    shadowBlur: 10,
                                    shadowColor: 'yellow'
                                }
                            },
                            zlevel: 1
                        },

                    ]
                };
                myChart.setOption(option);
            }
        },
        mounted() {
            this.refreshMap();
        }
    });
</script>
<script>

</script>
</html>
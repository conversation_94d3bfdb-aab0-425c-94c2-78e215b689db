package com.lcc.localStreamData.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sun.org.apache.xpath.internal.operations.Bool;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class Dataset {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private String datasetName;
    private String filename;
    private Long size;
    private LocalDateTime createTime;
    private String permission;
    private Integer createUser;
    private String status;
}

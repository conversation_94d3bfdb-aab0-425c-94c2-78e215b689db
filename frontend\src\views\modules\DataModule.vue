<template>
  <div class="data-module">
    <el-card>
      <template #header>
        <div class="module-header">
          <h2>数据管理&预处理</h2>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>数据管理&预处理</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </template>

      <!-- 子模块导航 -->
      <div class="sub-navigation">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="流数据管理" name="management">
            <template #label>
              <span class="tab-label">
                <el-icon><DataLine /></el-icon>
                流数据管理
              </span>
            </template>
          </el-tab-pane>
          <el-tab-pane label="数据集管理" name="dataset">
            <template #label>
              <span class="tab-label">
                <el-icon><FolderOpened /></el-icon>
                数据集管理
              </span>
            </template>
          </el-tab-pane>
          <el-tab-pane label="数据预处理" name="process">
            <template #label>
              <span class="tab-label">
                <el-icon><Setting /></el-icon>
                数据预处理
              </span>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 子页面内容 -->
      <div class="module-content">
        <router-view />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { DataLine, FolderOpened, Setting } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const activeTab = ref('management')

// 根据当前路由设置活跃标签
const setActiveTabFromRoute = () => {
  const routeName = route.name
  switch (routeName) {
    case 'DataManagement':
      activeTab.value = 'management'
      break
    case 'DatasetManagement':
      activeTab.value = 'dataset'
      break
    case 'DataProcess':
      activeTab.value = 'process'
      break
    default:
      activeTab.value = 'management'
  }
}

// 当前页面标题
const currentPageTitle = computed(() => {
  switch (activeTab.value) {
    case 'management':
      return '流数据管理'
    case 'dataset':
      return '数据集管理'
    case 'process':
      return '数据预处理'
    default:
      return '流数据管理'
  }
})

// 标签切换处理
const handleTabChange = (tabName) => {
  switch (tabName) {
    case 'management':
      router.push('/data/management')
      break
    case 'dataset':
      router.push('/data/dataset')
      break
    case 'process':
      router.push('/data/process')
      break
  }
}

// 监听路由变化
watch(() => route.name, () => {
  setActiveTabFromRoute()
}, { immediate: true })
</script>

<style scoped>
.data-module {
  height: 100%;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.module-header h2 {
  margin: 0;
  color: #333;
}

.sub-navigation {
  margin-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.module-content {
  min-height: 500px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-tabs__header) {
  margin: 0 0 20px 0;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__item) {
  padding: 0 20px;
  height: 50px;
  line-height: 50px;
  font-size: 14px;
}

:deep(.el-tabs__item.is-active) {
  color: #409EFF;
  font-weight: 600;
}
</style>

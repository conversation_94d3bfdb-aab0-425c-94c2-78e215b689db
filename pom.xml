<!--<?xml version="1.0" encoding="UTF-8"?>-->
<!--<project xmlns="http://maven.apache.org/POM/4.0.0"-->
<!--         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"-->
<!--         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">-->
<!--    <modelVersion>4.0.0</modelVersion>-->
<!--    <parent>-->
<!--        <groupId>org.springframework.boot</groupId>-->
<!--        <artifactId>spring-boot-starter-parent</artifactId>-->
<!--        <version>2.4.5</version>-->
<!--        <relativePath/>-->
<!--    </parent>-->
<!--    <groupId>com.lcc</groupId>-->
<!--    <artifactId>local_stream_data</artifactId>-->
<!--    <version>1.0</version>-->
<!--    <properties>-->
<!--        <java.version>1.8</java.version>-->
<!--    </properties>-->


<!--    <dependencies>-->

<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-test</artifactId>-->
<!--            <scope>test</scope>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-web</artifactId>-->
<!--            <scope>compile</scope>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.baomidou</groupId>-->
<!--            <artifactId>mybatis-plus-boot-starter</artifactId>-->
<!--            <version>3.4.2</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.projectlombok</groupId>-->
<!--            <artifactId>lombok</artifactId>-->
<!--            <version>1.18.20</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.alibaba</groupId>-->
<!--            <artifactId>fastjson</artifactId>-->
<!--            <version>1.2.76</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>commons-lang</groupId>-->
<!--            <artifactId>commons-lang</artifactId>-->
<!--            <version>2.6</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>mysql</groupId>-->
<!--            <artifactId>mysql-connector-java</artifactId>-->
<!--            <scope>runtime</scope>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.alibaba</groupId>-->
<!--            <artifactId>druid-spring-boot-starter</artifactId>-->
<!--            <version>1.1.23</version>-->
<!--        </dependency>-->

<!--        &lt;!&ndash;        &lt;!&ndash;Security&ndash;&gt;&ndash;&gt;-->
<!--        &lt;!&ndash;        <dependency>&ndash;&gt;-->
<!--        &lt;!&ndash;            <groupId>org.springframework.boot</groupId>&ndash;&gt;-->
<!--        &lt;!&ndash;            <artifactId>spring-boot-starter-security</artifactId>&ndash;&gt;-->
<!--        &lt;!&ndash;        </dependency>&ndash;&gt;-->
<!--        &lt;!&ndash; 验证码 &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>pro.fessional</groupId>-->
<!--            <artifactId>kaptcha</artifactId>-->
<!--            <version>2.3.3</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash; excel工具&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.apache.poi</groupId>-->
<!--            <artifactId>poi-ooxml</artifactId>-->
<!--            <version>4.1.2</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash; Feign远程调用&ndash;&gt;-->
<!--&lt;!&ndash;        <dependency>&ndash;&gt;-->
<!--        &lt;!&ndash;            <groupId>org.springframework.cloud</groupId>&ndash;&gt;-->
<!--        &lt;!&ndash;            <artifactId>spring-cloud-dependencies</artifactId>&ndash;&gt;-->
<!--        &lt;!&ndash;            <version>2020.0.6</version>&ndash;&gt;-->
<!--        &lt;!&ndash;            <type>pom</type>&ndash;&gt;-->
<!--        &lt;!&ndash;            <scope>import</scope>&ndash;&gt;-->
<!--        &lt;!&ndash;        </dependency>&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.springframework.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-openfeign</artifactId>-->
<!--            <version>3.0.7</version>-->
<!--        </dependency>-->
<!--    </dependencies>-->

<!--    <build>-->
<!--        <plugins>-->
<!--            <plugin>-->
<!--                <groupId>org.springframework.boot</groupId>-->
<!--                <artifactId>spring-boot-maven-plugin</artifactId>-->
<!--                <version>2.4.5</version>-->
<!--            </plugin>-->
<!--        </plugins>-->
<!--    </build>-->


<!--</project>-->

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.4.5</version>
        <relativePath/>
    </parent>
    <groupId>com.lcc</groupId>
    <artifactId>local_stream_data</artifactId>
    <version>1.0</version>
    <properties>
        <java.version>1.8</java.version>
        <!-- 添加 MyBatis-Plus 注解依赖的版本控制 -->
        <mybatis-plus.version>3.4.2</mybatis-plus.version>
    </properties>

    <dependencies>
        <!-- Spring Boot 基础依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>compile</scope>
        </dependency>

        <!-- MyBatis-Plus 核心依赖 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>

        <!-- 关键修复：添加 MyBatis-Plus 注解依赖 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>

        <!-- 其他依赖保持不变 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.20</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.76</version>
        </dependency>

        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.25</version> <!-- 明确指定版本确保兼容性 -->
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.1.23</version>
        </dependency>

        <!-- 验证码 -->
        <dependency>
            <groupId>pro.fessional</groupId>
            <artifactId>kaptcha</artifactId>
            <version>2.3.3</version>
        </dependency>

        <!-- excel工具-->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>

        <!-- Feign远程调用-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>3.0.7</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.4.5</version>
                <configuration>
                    <!-- 添加类扫描配置 -->
                    <classifier>exec</classifier>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
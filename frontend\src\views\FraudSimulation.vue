<template>
  <div class="fraud-simulation">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>金融欺诈流数据模拟</span>
          <el-button type="primary" @click="startSimulation" :loading="isSimulating">
            {{ isSimulating ? '模拟中...' : '开始模拟' }}
          </el-button>
        </div>
      </template>

      <!-- 模拟配置 -->
      <div class="simulation-config">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card shadow="never">
              <template #header>
                <h4>模拟参数配置</h4>
              </template>
              <el-form :model="simulationConfig" label-width="120px">
                <el-form-item label="数据量">
                  <el-input-number 
                    v-model="simulationConfig.dataCount" 
                    :min="100" 
                    :max="10000" 
                    :step="100"
                  />
                </el-form-item>
                <el-form-item label="欺诈比例">
                  <el-slider 
                    v-model="simulationConfig.fraudRate" 
                    :min="1" 
                    :max="20" 
                    show-stops
                    :format-tooltip="formatTooltip"
                  />
                </el-form-item>
                <el-form-item label="生成速度">
                  <el-select v-model="simulationConfig.speed" placeholder="选择生成速度">
                    <el-option label="慢速 (1条/秒)" value="slow" />
                    <el-option label="中速 (10条/秒)" value="medium" />
                    <el-option label="快速 (100条/秒)" value="fast" />
                  </el-select>
                </el-form-item>
                <el-form-item label="数据类型">
                  <el-checkbox-group v-model="simulationConfig.dataTypes">
                    <el-checkbox label="transaction">交易数据</el-checkbox>
                    <el-checkbox label="user">用户行为</el-checkbox>
                    <el-checkbox label="device">设备信息</el-checkbox>
                    <el-checkbox label="location">地理位置</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card shadow="never">
              <template #header>
                <h4>实时统计</h4>
              </template>
              <div class="statistics">
                <div class="stat-item">
                  <div class="stat-value">{{ statistics.totalGenerated }}</div>
                  <div class="stat-label">已生成数据</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ statistics.fraudCount }}</div>
                  <div class="stat-label">欺诈数据</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ statistics.normalCount }}</div>
                  <div class="stat-label">正常数据</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ statistics.currentRate }}</div>
                  <div class="stat-label">当前速率(条/秒)</div>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card shadow="never">
              <template #header>
                <h4>模拟状态</h4>
              </template>
              <div class="status-panel">
                <el-tag :type="statusType" size="large">{{ statusText }}</el-tag>
                <div class="progress-info">
                  <el-progress 
                    :percentage="progress" 
                    :status="progressStatus"
                    :stroke-width="8"
                  />
                  <p>进度: {{ progress }}%</p>
                </div>
                <div class="control-buttons">
                  <el-button @click="pauseSimulation" :disabled="!isSimulating">暂停</el-button>
                  <el-button @click="stopSimulation" :disabled="!isSimulating">停止</el-button>
                  <el-button @click="resetSimulation">重置</el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 数据预览 -->
      <div class="data-preview">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <span>生成数据预览</span>
              <el-button @click="exportData" :disabled="!generatedData.length">导出数据</el-button>
            </div>
          </template>
          <el-table 
            :data="generatedData.slice(0, 10)" 
            style="width: 100%"
            max-height="400"
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="timestamp" label="时间戳" width="180" />
            <el-table-column prop="userId" label="用户ID" width="120" />
            <el-table-column prop="amount" label="金额" width="100" />
            <el-table-column prop="location" label="地点" width="120" />
            <el-table-column prop="deviceId" label="设备ID" width="120" />
            <el-table-column prop="isFraud" label="是否欺诈" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.isFraud ? 'danger' : 'success'">
                  {{ scope.row.isFraud ? '欺诈' : '正常' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="riskScore" label="风险评分" width="100" />
          </el-table>
          <div v-if="generatedData.length > 10" class="table-footer">
            <p>显示前10条数据，共{{ generatedData.length }}条</p>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

const isSimulating = ref(false)
const simulationTimer = ref(null)

const simulationConfig = reactive({
  dataCount: 1000,
  fraudRate: 5,
  speed: 'medium',
  dataTypes: ['transaction', 'user']
})

const statistics = reactive({
  totalGenerated: 0,
  fraudCount: 0,
  normalCount: 0,
  currentRate: 0
})

const generatedData = ref([])

const statusType = computed(() => {
  if (isSimulating.value) return 'warning'
  if (statistics.totalGenerated > 0) return 'success'
  return 'info'
})

const statusText = computed(() => {
  if (isSimulating.value) return '模拟中'
  if (statistics.totalGenerated > 0) return '已完成'
  return '待开始'
})

const progress = computed(() => {
  if (simulationConfig.dataCount === 0) return 0
  return Math.min(100, (statistics.totalGenerated / simulationConfig.dataCount) * 100)
})

const progressStatus = computed(() => {
  if (progress.value === 100) return 'success'
  if (isSimulating.value) return 'active'
  return 'normal'
})

const formatTooltip = (val) => `${val}%`

// 生成模拟数据
const generateData = () => {
  const isFraud = Math.random() * 100 < simulationConfig.fraudRate
  const data = {
    id: statistics.totalGenerated + 1,
    timestamp: new Date().toISOString(),
    userId: `USER_${Math.floor(Math.random() * 10000)}`,
    amount: Math.floor(Math.random() * 10000) + 100,
    location: ['北京', '上海', '广州', '深圳', '杭州'][Math.floor(Math.random() * 5)],
    deviceId: `DEV_${Math.floor(Math.random() * 1000)}`,
    isFraud,
    riskScore: isFraud ? Math.floor(Math.random() * 40) + 60 : Math.floor(Math.random() * 40) + 10
  }
  
  generatedData.value.unshift(data)
  statistics.totalGenerated++
  
  if (isFraud) {
    statistics.fraudCount++
  } else {
    statistics.normalCount++
  }
}

// 开始模拟
const startSimulation = () => {
  if (isSimulating.value) return
  
  isSimulating.value = true
  const speedMap = {
    slow: 1000,
    medium: 100,
    fast: 10
  }
  
  const interval = speedMap[simulationConfig.speed]
  statistics.currentRate = 1000 / interval
  
  simulationTimer.value = setInterval(() => {
    if (statistics.totalGenerated >= simulationConfig.dataCount) {
      stopSimulation()
      ElMessage.success('数据模拟完成！')
      return
    }
    
    generateData()
  }, interval)
  
  ElMessage.success('开始生成模拟数据')
}

// 暂停模拟
const pauseSimulation = () => {
  if (simulationTimer.value) {
    clearInterval(simulationTimer.value)
    simulationTimer.value = null
  }
  isSimulating.value = false
  statistics.currentRate = 0
  ElMessage.info('模拟已暂停')
}

// 停止模拟
const stopSimulation = () => {
  pauseSimulation()
  ElMessage.info('模拟已停止')
}

// 重置模拟
const resetSimulation = () => {
  stopSimulation()
  statistics.totalGenerated = 0
  statistics.fraudCount = 0
  statistics.normalCount = 0
  statistics.currentRate = 0
  generatedData.value = []
  ElMessage.info('模拟已重置')
}

// 导出数据
const exportData = () => {
  const dataStr = JSON.stringify(generatedData.value, null, 2)
  const blob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `fraud_simulation_data_${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('数据导出成功')
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.simulation-config {
  margin-bottom: 20px;
}

.statistics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.status-panel {
  text-align: center;
  padding: 20px;
}

.progress-info {
  margin: 20px 0;
}

.control-buttons {
  margin-top: 20px;
}

.control-buttons .el-button {
  margin: 0 5px;
}

.data-preview {
  margin-top: 20px;
}

.table-footer {
  text-align: center;
  padding: 10px;
  color: #666;
}
</style>

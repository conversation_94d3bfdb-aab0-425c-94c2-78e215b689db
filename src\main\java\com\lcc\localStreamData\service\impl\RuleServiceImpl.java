package com.lcc.localStreamData.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lcc.localStreamData.entity.Rule;
import com.lcc.localStreamData.mapper.RuleMapper;
import com.lcc.localStreamData.service.RuleService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RuleServiceImpl extends ServiceImpl<RuleMapper, Rule> implements RuleService {
    @Override
    public List<Rule> findListByRuleGroupId(Integer id) {
        QueryWrapper<Rule> wrapper = new QueryWrapper<>();
        wrapper.eq("rule_group_id", id);
        return this.list(wrapper);
    }

    @Override
    public void removeOldRules(Integer ruleGroupId, List<Integer> newRuleIdList) {
        if (ruleGroupId == null) return;//规则组id不能为空
        //删除规则组中新规则列表不包含的规则
        QueryWrapper<Rule> wrapper = new QueryWrapper<>();
        wrapper.eq("rule_group_id", ruleGroupId);
        wrapper.notIn(!newRuleIdList.isEmpty(), "id", newRuleIdList);
        this.remove(wrapper);
    }
}

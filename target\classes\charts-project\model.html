<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/elementui.css">
    <style>
        body {
            background-color: #fff;
        }
        #app {
            padding: 20px;
        }
        .page {
            margin-top: 30px;
            text-align: center;
        }
        .header {

        }
        .header .search {
            width: 230px;
        }
        .table {
            border: 2px solid #f3f4f7;
            margin-top: 20px;
        }
        .bread {
            margin-bottom: 20px;
        }
        .switch {
            margin-top: 8px;
        }
    </style>
</head>
<body>
<div id="app">

    <div class="bread">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item><a href="/">首页</a></el-breadcrumb-item>
            <el-breadcrumb-item><a href="/">数据管理</a></el-breadcrumb-item>
        </el-breadcrumb>
    </div>

    <div class="header">
        <el-row :gutter="20">
            <el-col :span="6">
                <div class="search">
                    <el-input
                            placeholder="请输入内容"
                            prefix-icon="el-icon-search"
                            v-model="condition">
                    </el-input>
                </div>
            </el-col>
            <el-col :span="6" :offset="12">
                <div class="switch">
                    <el-switch
                            style="display: block"
                            v-model="opt"
                            active-color="#409eff"
                            inactive-color="#ff4949"
                            active-text="开启模拟"
                            inactive-text="关闭模拟">
                    </el-switch>
                </div>
            </el-col>
        </el-row>


    </div>
    <div class="table">
        <el-table
                :data="modelData"
                stripe
                style="width: 100%">
            <el-table-column
                    type="index"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="time"
                    label="发布日期"
                    width="320">
            </el-table-column>
            <el-table-column
                    prop="modelName"
                    label="模型名称"
                    width="340">
            </el-table-column>
            <el-table-column
                    prop="modelDescription"
                    label="模型介绍"
                    width="350">
            </el-table-column>
            <el-table-column
                    prop="trainingParameters"
                    label="训练参数"
                    width="350"
            >
            </el-table-column>
            <el-table-column
                    prop="trainer"
                    label="训练人">
            </el-table-column>
        </el-table>
    </div>

    <div class="page">
        <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400">
        </el-pagination>
    </div>

</div>
</body>
<script src="js/vue.js"></script>
<script src="js/axios.js"></script>
<script src="js/elementui.js"></script>
<script>
    new Vue({
        el: "#app",
        data() {
            return {
                modelData: [
                    {
                        modelDate: '2016-05-02 14:59',
                        modelName: 'K-近邻算法',
                        modelInfo: '新数据可以直接加入数据集',
                        modelUser: '王小虎',
                        modelParameter: 'K值=3'
                    },
                    {
                        modelDate: '2016-05-02 14:59',
                        modelName: '线性回归',
                        modelInfo: '新数据可以直接加入数据集',
                        modelUser: '王小虎',
                        modelParameter: 'K值=3'
                    },
                    {
                        modelDate: '2016-05-02 14:59',
                        modelName: '决策树',
                        modelInfo: '新数据可以直接加入数据集',
                        modelUser: '王小虎',
                        modelParameter: 'K值=3'
                    },
                    {
                        modelDate: '2016-05-02 14:59',
                        modelName: '逻辑回归',
                        modelInfo: '新数据可以直接加入数据集',
                        modelUser: '王小虎',
                        modelParameter: 'K值=3'
                    },
                    {
                        modelDate: '2016-05-02 14:59',
                        modelName: 'K-近邻算法',
                        modelInfo: '新数据可以直接加入数据集',
                        modelUser: '王小虎',
                        modelParameter: 'K值=3'
                    },
                    {
                        modelDate: '2016-05-02 14:59',
                        modelName: 'K-近邻算法',
                        modelInfo: '新数据可以直接加入数据集',
                        modelUser: '王小虎',
                        modelParameter: 'K值=3'
                    },
                    {
                        modelDate: '2016-05-02 14:59',
                        modelName: 'K-近邻算法',
                        modelInfo: '新数据可以直接加入数据集',
                        modelUser: '王小虎',
                        modelParameter: 'K值=3'
                    }
                ],
                currentPage: 1,
                condition: "",
                opt: ""
            }
        },
        methods: {
            handleSizeChange(val) {
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                console.log(`当前页: ${val}`);
            },
            goBack() {
                console.log('go back');
            }
        },
    });
</script>
</html>
# 金融流数据在线分析系统 - 前端

基于 Vue 3 + Vite + Element Plus 构建的现代化前端应用，整合了原有 resources 中的所有功能。

## 功能特性

- 🚀 基于 Vue 3 Composition API
- ⚡ Vite 构建工具，开发体验极佳
- 🎨 Element Plus UI 组件库
- 📊 ECharts 图表库集成
- 🛣️ Vue Router 4 路由管理
- 📱 响应式设计
- 🔐 用户认证和权限管理

## 主要功能模块

- **用户管理** - 用户注册、登录、权限控制
- **Dashboard** - 数据概览、实时监控、图表展示
- **数据管理** - 流数据管理、数据集管理、数据预处理
- **模型管理** - 规则管理、模型训练
- **风险评估** - 多种评估方式、融合评估
- **用户预警** - 实时预警功能
- **平台监控** - HDFS、YARN、MapReduce 监控

## 技术栈

- **框架**: Vue 3.5.17
- **构建工具**: Vite 7.0.0
- **UI 库**: Element Plus 2.10.2
- **图表**: ECharts 5.6.0
- **路由**: Vue Router 4.5.1
- **HTTP 客户端**: Axios 1.10.0
- **包管理器**: pnpm

## 开发环境设置

### 前置要求

- Node.js >= 16
- pnpm >= 8

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

应用将在 http://localhost:3000 启动（如果端口被占用会自动选择其他端口）

### 构建生产版本

```bash
pnpm build
```

### 预览生产构建

```bash
pnpm preview
```

## 项目结构

```
frontend/
├── public/                 # 静态资源
│   ├── css/               # 原有 CSS 文件
│   ├── js/                # 原有 JS 文件
│   ├── images/            # 图片资源
│   ├── fonts/             # 字体文件
│   └── plugins/           # 插件文件
├── src/
│   ├── views/             # 页面组件
│   │   ├── Layout.vue     # 主布局
│   │   ├── Login.vue      # 登录页
│   │   ├── Dashboard.vue  # 仪表板
│   │   └── ...           # 其他功能页面
│   ├── router/            # 路由配置
│   ├── App.vue           # 根组件
│   ├── main.js           # 入口文件
│   └── style.css         # 全局样式
├── package.json
├── vite.config.js        # Vite 配置
└── README.md
```

## 后端接口代理

开发环境下，前端会自动代理以下接口到后端服务器（默认 http://localhost:8080）：

- `/login` - 用户登录
- `/logout` - 用户登出
- `/register` - 用户注册
- `/captcha` - 验证码
- `/users` - 用户管理
- `/overview` - 概览数据
- `/normal` - 异常数据

## 部署说明

1. 构建生产版本：`pnpm build`
2. 将 `dist` 目录部署到 Web 服务器
3. 配置反向代理将 API 请求转发到后端服务器

## 从原有系统迁移的改进

1. **现代化技术栈** - 从 Vue 2 升级到 Vue 3
2. **组件化架构** - 替换原有的 iframe 导航方式
3. **类型安全** - 更好的开发体验
4. **性能优化** - Vite 构建工具提供更快的开发和构建速度
5. **响应式设计** - 更好的移动端适配
6. **代码组织** - 更清晰的项目结构和代码组织

## 开发指南

### 添加新页面

1. 在 `src/views/` 下创建新的 Vue 组件
2. 在 `src/router/index.js` 中添加路由配置
3. 在 `Layout.vue` 中添加菜单项（如需要）

### 样式定制

- 全局样式在 `src/style.css` 中定义
- 组件样式使用 `<style scoped>`
- Element Plus 主题定制可在 `main.js` 中配置

### API 调用

使用 axios 进行 HTTP 请求，已在 `main.js` 中全局配置。

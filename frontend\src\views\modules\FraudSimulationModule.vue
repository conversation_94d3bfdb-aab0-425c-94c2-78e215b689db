<template>
  <div class="fraud-simulation-module">
    <el-card>
      <template #header>
        <div class="module-header">
          <h2>金融欺诈流数据模拟</h2>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>金融欺诈流数据模拟</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </template>

      <!-- 子模块导航 -->
      <div class="sub-navigation">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="金融欺诈流数据模拟" name="simulation">
            <template #label>
              <span class="tab-label">
                <el-icon><DataAnalysis /></el-icon>
                金融欺诈流数据模拟
              </span>
            </template>
          </el-tab-pane>
          <el-tab-pane label="流数据生成与处理" name="stream-generation">
            <template #label>
              <span class="tab-label">
                <el-icon><Operation /></el-icon>
                流数据生成与处理
              </span>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 子页面内容 -->
      <div class="module-content">
        <router-view />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { DataAnalysis, Operation } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const activeTab = ref('simulation')

// 根据当前路由设置活跃标签
const setActiveTabFromRoute = () => {
  const routeName = route.name
  switch (routeName) {
    case 'FraudSimulation':
      activeTab.value = 'simulation'
      break
    case 'StreamGeneration':
      activeTab.value = 'stream-generation'
      break
    default:
      activeTab.value = 'simulation'
  }
}

// 当前页面标题
const currentPageTitle = computed(() => {
  switch (activeTab.value) {
    case 'simulation':
      return '金融欺诈流数据模拟'
    case 'stream-generation':
      return '流数据生成与处理'
    default:
      return '金融欺诈流数据模拟'
  }
})

// 标签切换处理
const handleTabChange = (tabName) => {
  switch (tabName) {
    case 'simulation':
      router.push('/fraud-simulation/simulation')
      break
    case 'stream-generation':
      router.push('/fraud-simulation/stream-generation')
      break
  }
}

// 监听路由变化
watch(() => route.name, () => {
  setActiveTabFromRoute()
}, { immediate: true })
</script>

<style scoped>
.fraud-simulation-module {
  height: 100%;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.module-header h2 {
  margin: 0;
  color: #333;
}

.sub-navigation {
  margin-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.module-content {
  min-height: 500px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-tabs__header) {
  margin: 0 0 20px 0;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__item) {
  padding: 0 20px;
  height: 50px;
  line-height: 50px;
  font-size: 14px;
}

:deep(.el-tabs__item.is-active) {
  color: #409EFF;
  font-weight: 600;
}
</style>

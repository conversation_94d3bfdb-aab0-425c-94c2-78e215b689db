<template>
  <div class="viewport">
    <div class="column">
      <!-- 概览区域 -->
      <div class="panel overview">
        <div class="inner">
          <ul>
            <li>
              <h4>{{ (overView.totalMoney / 10000000).toFixed(1) }}</h4>
              <span>
                <i class="icon-dot"></i>
                消费金额(千万元)
              </span>
            </li>
            <li class="item">
              <h4>{{ overView.users }}</h4>
              <span>
                <i class="icon-dot" style="color: #6acca3"></i>
                消费人数
              </span>
            </li>
            <li>
              <h4>{{ overView.places }}</h4>
              <span>
                <i class="icon-dot" style="color: #6acca3"></i>
                消费地点
              </span>
            </li>
            <li>
              <h4>{{ overView.totalNums }}</h4>
              <span>
                <i class="icon-dot" style="color: #ed3f35"></i>
                消费总次数
              </span>
            </li>
          </ul>
        </div>
      </div>

      <!-- 监控区域 -->
      <div class="panel monitor">
        <div class="inner">
          <div class="tabs">
            <a href="javascript:;" class="active">异常消费监控</a>
          </div>
          <div class="content" style="display: block">
            <div class="head">
              <span class="col">消费时间</span>
              <span class="col">消费地点</span>
              <span class="col">消费金额(元)</span>
            </div>
            <div class="marquee-view">
              <div class="marquee" id="marquee_move">
                <div class="row" v-for="item in normalData" :key="item.id">
                  <span class="col">{{ item.streamTimeMinute }}</span>
                  <span class="col">{{ item.streamConsumeLocation }}</span>
                  <span class="col">{{ item.streamMoney }}</span>
                  <span class="icon-dot"></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 消费类型统计 -->
      <div class="point panel">
        <div class="inner">
          <h3>消费类型统计</h3>
          <div class="chart">
            <div class="pie" ref="pieChartRef"></div>
            <div class="data">
              <div class="item">
                <h4>{{ pieTitle }}</h4>
                <span>
                  <i class="icon-dot" style="color: #ed3f35"></i>
                  Top1
                </span>
              </div>
              <div class="item">
                <h4>{{ pieTypeNums }}</h4>
                <span>
                  <i class="icon-dot" style="color: #eacf19"></i>
                  消费类型数
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="column">
      <!-- 地图 -->
      <div class="map">
        <h3>
          <span class="icon-cube"></span>
          异常消费分布
        </h3>
        <div class="chart">
          <div class="geo" ref="mapChartRef"></div>
        </div>
      </div>

      <!-- 用户 -->
      <div class="users panel">
        <div class="inner">
          <h3>注册地消费总量(Top13)</h3>
          <div class="chart">
            <div class="bar" ref="barChartRef"></div>
            <div class="data">
              <div class="item">
                <h4>{{ (overView.totalMoney / 10000).toFixed(1) }}</h4>
                <span>
                  <i class="icon-dot" style="color: #ed3f35"></i>
                  消费总量(万元)
                </span>
              </div>
              <div class="item">
                <h4>{{ maxCity }}</h4>
                <span>
                  <i class="icon-dot" style="color: #eacf19"></i>
                  Top1
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="column">
      <!-- 订单 -->
      <div class="order panel">
        <div class="inner">
          <!-- 筛选 -->
          <div class="filter">
            <a href="javascript:;" :class="{ active: normalByTimeIndex == 0 }" @click="normalByTime('morning')">早</a>
            <a href="javascript:;" :class="{ active: normalByTimeIndex == 1 }" @click="normalByTime('noon')">中</a>
            <a href="javascript:;" :class="{ active: normalByTimeIndex == 2 }" @click="normalByTime('night')">晚</a>
          </div>
          <!-- 数据 -->
          <div class="data">
            <div class="item">
              <h4>{{ normalByTimeData.totalCount }}</h4>
              <span>
                <i class="icon-dot" style="color: #ed3f35;"></i>
                总订单数
              </span>
            </div>
            <div class="item">
              <h4>{{ normalByTimeData.notNormalCount }}</h4>
              <span>
                <i class="icon-dot" style="color: #eacf19;"></i>
                异常消费数
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 销售额 -->
      <div class="sales panel">
        <div class="inner">
          <div class="caption">
            <h3>销售额统计</h3>
            <a href="javascript:;" :class="{ active: morningIsActive }" @click="morning">早</a>
            <a href="javascript:;" :class="{ active: noonIsActive }" @click="noon">中</a>
            <a href="javascript:;" :class="{ active: nightIsActive }" @click="night">晚</a>
          </div>
          <div class="chart">
            <div class="label">单位:千万</div>
            <div class="line" ref="lineChartRef"></div>
          </div>
        </div>
      </div>

      <!-- 渠道 季度 -->
      <div class="wrap">
        <div class="channel panel">
          <div class="inner">
            <h3>注册地分布</h3>
            <div class="data">
              <div class="radar" ref="radarChartRef"></div>
            </div>
          </div>
        </div>
        <div class="quarter panel">
          <div class="inner">
            <h3>异常消费占比</h3>
            <div class="chart">
              <div class="box">
                <div class="gauge" ref="gaugeChartRef"></div>
                <div class="label">{{ normalPercentData.percent }}<small> %</small></div>
              </div>
              <div class="data">
                <div class="item">
                  <h4>{{ normalPercentData.totalCount }}</h4>
                  <span>
                    <i class="icon-dot" style="color: #6acca3"></i>
                    总消费数
                  </span>
                </div>
                <div class="item">
                  <h4>{{ normalPercentData.notNormalCount }}</h4>
                  <span>
                    <i class="icon-dot" style="color: #ed3f35"></i>
                    异常消费数
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 排行榜 -->
      <div class="top panel">
        <div class="inner">
          <div class="all">
            <h3>全国热榜</h3>
            <ul>
              <li v-for="(item, index) in nationHot" :key="index">
                <i class="icon-cup1" style="color: #d93f36;" v-if="index == 0"></i>
                <i class="icon-cup2" style="color: #68d8fe;" v-if="index == 1"></i>
                <i class="icon-cup3" style="color: #4c9bfd;" v-if="index == 2"></i>
                {{ item.name }}
              </li>
            </ul>
          </div>
          <div class="province">
            <h3>各省热销 </h3>
            <div class="data">
              <ul class="sup">
                <li v-for="(item, index) in hotProvince" :key="index" :class="{ active: index == provinceActiveIndex }"
                  @mouseenter="provinceChange(item.city, index)">
                  <span>{{ item.city + " " }}</span>
                  <span> ￥{{ (item.sales / 1000000).toFixed(1) }} </span>
                </li>
              </ul>
              <ul class="sub">
                <li v-for="(item, index) in hotSale" :key="index">
                  <span>{{ item.name + " " }} </span>
                  <span> ￥{{ (item.num / 1000000).toFixed(1) }} </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import axios from 'axios'

const pieChartRef = ref()
const mapChartRef = ref()
const barChartRef = ref()
const lineChartRef = ref()
const radarChartRef = ref()
const gaugeChartRef = ref()

const overView = reactive({
  totalMoney: 88888888,
  users: 1234,
  places: 567,
  totalNums: 9876
})

const normalData = ref([
  { id: 1, streamTimeMinute: '09:30', streamConsumeLocation: '北京市', streamMoney: '1500' },
  { id: 2, streamTimeMinute: '10:15', streamConsumeLocation: '上海市', streamMoney: '2300' },
  { id: 3, streamTimeMinute: '11:20', streamConsumeLocation: '广州市', streamMoney: '1800' },
  { id: 4, streamTimeMinute: '14:30', streamConsumeLocation: '深圳市', streamMoney: '3200' },
  { id: 5, streamTimeMinute: '15:45', streamConsumeLocation: '杭州市', streamMoney: '2100' }
])

const pieTitle = ref('餐饮')
const pieTypeNums = ref(5)
const maxCity = ref('北京市')

const normalByTimeIndex = ref(0)
const normalByTimeData = reactive({
  totalCount: 1234,
  notNormalCount: 89
})

const morningIsActive = ref(true)
const noonIsActive = ref(false)
const nightIsActive = ref(false)

const normalPercentData = reactive({
  percent: 7.2,
  totalCount: 1234,
  notNormalCount: 89
})

// 排行榜数据
const nationHot = ref([
  { name: '北京市' },
  { name: '上海市' },
  { name: '广州市' },
  { name: '深圳市' },
  { name: '杭州市' }
])

const hotProvince = ref([
  { city: '北京', sales: 12000000 },
  { city: '上海', sales: 10000000 },
  { city: '广州', sales: 8000000 },
  { city: '深圳', sales: 7000000 },
  { city: '杭州', sales: 6000000 }
])

const hotSale = ref([
  { name: '商品A', num: 2000000 },
  { name: '商品B', num: 1800000 },
  { name: '商品C', num: 1500000 }
])

const provinceActiveIndex = ref(0)

// 方法定义
const normalByTime = (time) => {
  switch (time) {
    case 'morning':
      normalByTimeIndex.value = 0
      normalByTimeData.totalCount = 1234
      normalByTimeData.notNormalCount = 89
      break
    case 'noon':
      normalByTimeIndex.value = 1
      normalByTimeData.totalCount = 2345
      normalByTimeData.notNormalCount = 156
      break
    case 'night':
      normalByTimeIndex.value = 2
      normalByTimeData.totalCount = 3456
      normalByTimeData.notNormalCount = 234
      break
  }
}

const morning = () => {
  morningIsActive.value = true
  noonIsActive.value = false
  nightIsActive.value = false
}

const noon = () => {
  morningIsActive.value = false
  noonIsActive.value = true
  nightIsActive.value = false
}

const night = () => {
  morningIsActive.value = false
  noonIsActive.value = false
  nightIsActive.value = true
}

const provinceChange = (city, index) => {
  provinceActiveIndex.value = index
  // 根据选中的省份更新热销商品数据
  switch (city) {
    case '北京':
      hotSale.value = [
        { name: '商品A', num: 2000000 },
        { name: '商品B', num: 1800000 },
        { name: '商品C', num: 1500000 }
      ]
      break
    case '上海':
      hotSale.value = [
        { name: '商品D', num: 1900000 },
        { name: '商品E', num: 1700000 },
        { name: '商品F', num: 1400000 }
      ]
      break
    default:
      hotSale.value = [
        { name: '商品G', num: 1600000 },
        { name: '商品H', num: 1300000 },
        { name: '商品I', num: 1200000 }
      ]
  }
}

// 加载中国地图数据
const loadChinaMap = () => {
  return new Promise((resolve) => {
    if (window.chinaMapData) {
      resolve()
      return
    }

    const script = document.createElement('script')
    script.src = '/js/china.js'
    script.onload = () => {
      resolve()
    }
    document.head.appendChild(script)
  })
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  await loadChinaMap()

  // 饼图
  if (pieChartRef.value) {
    const pieChart = echarts.init(pieChartRef.value)
    const pieOption = {
      tooltip: {
        trigger: 'item'
      },
      legend: {
        show: false
      },
      series: [{
        name: '消费类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '50%'],
        data: [
          { value: 1048, name: '餐饮' },
          { value: 735, name: '购物' },
          { value: 580, name: '交通' },
          { value: 484, name: '娱乐' },
          { value: 300, name: '其他' }
        ],
        itemStyle: {
          borderRadius: 5,
          borderColor: '#fff',
          borderWidth: 2
        }
      }]
    }
    pieChart.setOption(pieOption)
  }

  // 柱状图
  if (barChartRef.value) {
    const barChart = echarts.init(barChartRef.value)
    const barOption = {
      grid: {
        top: '10%',
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都', '重庆', '西安', '天津', '苏州', '长沙'],
        axisLabel: {
          color: '#4c9bfd'
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#4c9bfd'
        }
      },
      series: [{
        data: [820, 932, 901, 934, 1290, 1330, 1320, 1200, 1100, 1000, 900, 800, 700],
        type: 'bar',
        itemStyle: {
          color: '#4c9bfd'
        }
      }]
    }
    barChart.setOption(barOption)
  }

  // 折线图
  if (lineChartRef.value) {
    const lineChart = echarts.init(lineChartRef.value)
    const lineOption = {
      grid: {
        top: '10%',
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
        axisLabel: {
          color: '#4c9bfd'
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#4c9bfd'
        }
      },
      series: [{
        data: [150, 230, 224, 218, 135, 147],
        type: 'line',
        smooth: true,
        itemStyle: {
          color: '#00d887'
        },
        lineStyle: {
          color: '#00d887'
        }
      }]
    }
    lineChart.setOption(lineOption)
  }

  // 地图
  if (mapChartRef.value) {
    const mapChart = echarts.init(mapChartRef.value)
    const mapOption = {
      tooltip: {
        trigger: 'item'
      },
      visualMap: {
        min: 0,
        max: 1000,
        left: 'left',
        top: 'bottom',
        text: ['高', '低'],
        calculable: true,
        inRange: {
          color: ['#50a3ba', '#eac736', '#d94e5d']
        }
      },
      series: [{
        name: '异常消费',
        type: 'map',
        map: 'china',
        roam: false,
        data: [
          { name: '北京', value: 177 },
          { name: '天津', value: 42 },
          { name: '河北', value: 102 },
          { name: '山西', value: 81 },
          { name: '内蒙古', value: 47 },
          { name: '辽宁', value: 67 },
          { name: '吉林', value: 82 },
          { name: '黑龙江', value: 123 },
          { name: '上海', value: 24 },
          { name: '江苏', value: 92 },
          { name: '浙江', value: 114 },
          { name: '安徽', value: 109 },
          { name: '福建', value: 116 },
          { name: '江西', value: 91 },
          { name: '山东', value: 119 },
          { name: '河南', value: 137 },
          { name: '湖北', value: 116 },
          { name: '湖南', value: 114 },
          { name: '重庆', value: 91 },
          { name: '四川', value: 125 },
          { name: '贵州', value: 62 },
          { name: '云南', value: 83 },
          { name: '西藏', value: 9 },
          { name: '陕西', value: 80 },
          { name: '甘肃', value: 56 },
          { name: '青海', value: 10 },
          { name: '宁夏', value: 18 },
          { name: '新疆', value: 67 },
          { name: '广东', value: 123 },
          { name: '广西', value: 59 },
          { name: '海南', value: 14 }
        ]
      }]
    }
    mapChart.setOption(mapOption)
  }

  // 雷达图
  if (radarChartRef.value) {
    const radarChart = echarts.init(radarChartRef.value)
    const radarOption = {
      radar: {
        indicator: [
          { name: '北京', max: 100 },
          { name: '上海', max: 100 },
          { name: '广州', max: 100 },
          { name: '深圳', max: 100 },
          { name: '杭州', max: 100 }
        ]
      },
      series: [{
        name: '注册地分布',
        type: 'radar',
        data: [{
          value: [80, 90, 70, 85, 75],
          name: '注册地分布'
        }]
      }]
    }
    radarChart.setOption(radarOption)
  }

  // 仪表盘
  if (gaugeChartRef.value) {
    const gaugeChart = echarts.init(gaugeChartRef.value)
    const gaugeOption = {
      series: [{
        name: '异常消费占比',
        type: 'gauge',
        detail: { formatter: '{value}%' },
        data: [{ value: 7.2, name: '异常占比' }]
      }]
    }
    gaugeChart.setOption(gaugeOption)
  }
}

// 获取数据
const fetchData = async () => {
  try {
    // 获取概览数据
    const overviewResponse = await axios.get('/overview')
    if (overviewResponse.data.code === 1) {
      Object.assign(overView, overviewResponse.data.data)
    }

    // 获取异常消费数据
    const normalResponse = await axios.get('/normal')
    if (normalResponse.data.code === 1) {
      normalData.value = normalResponse.data.data
    }
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

onMounted(async () => {
  await fetchData()
  await initCharts()
})
</script>

<style scoped>
/* 引入原始样式 */
@import url('/css/index.css');
@import url('/fonts/icomoon.css');

/* 自定义样式覆盖 */
.overview .inner span {
  font-size: 14px;
}

.data .item span {
  font-size: 14px;
}

/* 通过CSS3动画滚动marquee */
.marquee-view .marquee {
  animation: move 15s linear infinite;
}

@keyframes move {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-50%);
  }
}

/* 鼠标经过marquee 就停止动画 */
.marquee-view .marquee:hover {
  animation-play-state: paused;
}
</style>

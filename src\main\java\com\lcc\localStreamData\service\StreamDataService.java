package com.lcc.localStreamData.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lcc.localStreamData.entity.Rule;
import com.lcc.localStreamData.entity.StreamData;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface StreamDataService extends IService<StreamData> {

    public Page<StreamData> findList(int page, int pageSize, String place);

    public void mark(String id);

    public List<StreamData> findTodayList();

    List<Integer> judgeByRuleGroupId(Integer ruleGroupId, String[] dataIds);

    List<Integer> judgeByRuleList(List<StreamData> streamDataList, List<Rule> ruleList);

    public List getListByIds(String[] ids);

    public Page<StreamData> findUserList(int page, int pageSize);

    public Map userPaint(int userId);

    List<List<Integer>> judgeBatchByRuleGroupIds(String[] ruleGroupIds, String[] dataIds);

    void updateJudgeResult(List<String> streamDataIdList, List<Integer> result);

    void autoTestByDefaultRuleList(LocalDateTime start, LocalDateTime end, List<Rule> ruleList);
}

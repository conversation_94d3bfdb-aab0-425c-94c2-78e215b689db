package com.lcc.localStreamData.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lcc.localStreamData.entity.Rule;
import com.lcc.localStreamData.entity.StreamData;
import com.lcc.localStreamData.mapper.StreamDataMapper;
import com.lcc.localStreamData.service.RuleService;
import com.lcc.localStreamData.service.StreamDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class StreamDataServiceImpl extends ServiceImpl<StreamDataMapper, StreamData> implements StreamDataService {

    @Autowired
    private StreamDataMapper streamDataMapper;
    @Autowired
    private RuleService ruleService;

    @Override
    public Page<StreamData> findList(int page, int pageSize, String place) {

        //构造分页构造器
        Page pageInfo = new Page(page, pageSize);
        //构造条件构造器
        QueryWrapper<StreamData> queryWrapper = new QueryWrapper<>();
        //添加过滤条件
        if (StringUtils.isNotEmpty(place)) {
            queryWrapper.like("stream_consume_location", place)
                    .or().like("stream_sign_location", place);
        }
        queryWrapper.eq("stream_is_new", 1);
        queryWrapper.eq("stream_time_date", getNowDate());
        //添加排序条件
        queryWrapper.orderByDesc("stream_seconds");

        //执行查询
        Page result = this.page(pageInfo, queryWrapper);

        return result;
    }

    /**
     * 根据id将所属消费标为异常消费
     *
     * @param id
     */
    public void mark(String id) {
//        NormalData normalData = new NormalData();
//        normalData.setStreamIsNormal(0);//将normalData设置为异常消费数据
//        normalDataMapper.update(normalDataMapper.selectById(id),new UpdateWrapper<NormalData>(normalData));
        UpdateWrapper<StreamData> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("stream_id", id);
        updateWrapper.set("stream_is_normal", 0);
        streamDataMapper.update(null, updateWrapper);
    }


    public List<StreamData> findTodayList() {
        String nowDate = getNowDate();
        QueryWrapper<StreamData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("stream_time_date", nowDate);
        List<StreamData> list = streamDataMapper.selectList(queryWrapper);
        return list;
    }

    @Override
    public void updateJudgeResult(List<String> streamDataIdList, List<Integer> result) {
        if (streamDataIdList != null && result != null && streamDataIdList.size() == result.size()) {
            for (int i = 0; i < streamDataIdList.size(); i++) {
                UpdateWrapper<StreamData> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("stream_id", streamDataIdList.get(i));
                updateWrapper.set("stream_is_normal", result.get(i));
                streamDataMapper.update(null, updateWrapper);
            }
        }
    }

    @Override
    public void autoTestByDefaultRuleList(LocalDateTime start, LocalDateTime end, List<Rule> ruleList) {
        //查询start到end时间段内的数据
        QueryWrapper<StreamData> wrapper = new QueryWrapper<>();
        wrapper.between("stream_time", start, end);
        List<StreamData> streamDataList = this.list(wrapper);

        //评估
        List<Integer> result = this.judgeByRuleList(streamDataList, ruleList);

        //将结果写入数据库
        List<String> streamDataIds = streamDataList.stream()
                .map(StreamData::getStreamId).collect(Collectors.toList());
        this.updateJudgeResult(streamDataIds, result);
    }

    @Override
    public List<Integer> judgeByRuleGroupId(Integer ruleGroupId, String[] dataIds) {
        if (dataIds == null || dataIds.length == 0) {
            return new ArrayList<>();
        }
        List<Rule> ruleList = ruleService.findListByRuleGroupId(ruleGroupId);

        QueryWrapper<StreamData> wrapper = new QueryWrapper<>();
        wrapper.in("stream_id", Arrays.asList(dataIds));
        List<StreamData> streamDataList = this.list(wrapper);
        return judgeByRuleList(streamDataList, ruleList);
    }

    @Override
    public List<Integer> judgeByRuleList(List<StreamData> streamDataList, List<Rule> ruleList) {
        ArrayList<Integer> result = new ArrayList<>();
        for (StreamData streamData : streamDataList) {
            int isNormal = 1;
            for (Rule rule : ruleList) {
                boolean judgeResult = true;//默认是正常数据
                if ("1".equals(rule.getType())) {//限制高频消费
                    judgeResult = judgeByFrequencyLimitRule(streamData, rule.getTimeLimit(), rule.getFreqLimit());
                } else if ("2".equals(rule.getType())) {//限制异地消费
                    judgeResult = judgeDifferentLocationRule(streamData, rule.getTimeLimit());
                }

                if (!judgeResult) {//有规则判别为异常，提前结束循环
                    isNormal = 0;
                    break;
                }
            }
            result.add(isNormal);
        }
        return result;
    }

    /**
     * 使用高频消费规则校验
     */
    public boolean judgeByFrequencyLimitRule(StreamData streamData, Integer second, Integer count) {
        LocalDateTime streamTime = streamData.getStreamTime();
        LocalDateTime start = streamTime.minusSeconds(second);

        QueryWrapper<StreamData> wrapper = new QueryWrapper<>();
        wrapper.eq("stream_user_id", streamData.getStreamUserId());
        wrapper.between("stream_time", start, streamTime);
        Integer dataCount = streamDataMapper.selectCount(wrapper);

        return dataCount < count;//dataCount小于count说明是正常数据
    }

    /**
     * 使用异地消费规则校验
     */
    public boolean judgeDifferentLocationRule(StreamData streamData, Integer second) {
        LocalDateTime streamTime = streamData.getStreamTime();
        LocalDateTime start = streamTime.minusSeconds(second);

        QueryWrapper<StreamData> wrapper = new QueryWrapper<>();
        wrapper.eq("stream_user_id", streamData.getStreamUserId());
        wrapper.ne("stream_consume_location", streamData.getStreamConsumeLocation());
        wrapper.between("stream_time", start, streamTime);

        Integer dataCount = streamDataMapper.selectCount(wrapper);
        return dataCount == 0;//dataCount为0说明是正常数据
    }

    @Override
    public List getListByIds(String[] ids) {
        List<StreamData> list = streamDataMapper.selectBatchIds(Arrays.asList(ids));

        return list;
    }

    @Override
    public Page<StreamData> findUserList(int page, int pageSize) {

        //构造分页构造器
        Page pageInfo = new Page(page, pageSize);
        //构造条件构造器
        QueryWrapper<StreamData> queryWrapper = new QueryWrapper<>();
        //添加过滤条件
        queryWrapper.select("DISTINCT(stream_user_id),stream_sign_location");

        queryWrapper.eq("stream_time_date", getNowDate());


        //执行查询
        Page result = this.page(pageInfo, queryWrapper);

        return result;
    }

    @Override
    public Map userPaint(int userId) {

        HashMap<Object, Object> map = new HashMap<>();

        // 用户消费地点分布
        QueryWrapper<StreamData> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.select("stream_consume_location as name, count(*) as value ")
                .eq("stream_time_date", getNowDate())
                .eq("stream_user_id", userId)
                .groupBy("stream_consume_location");
        List<Map<String, Object>> geoList = streamDataMapper.selectMaps(queryWrapper1);

        // 用户消费类型分布
        QueryWrapper<StreamData> queryWrapper2 = new QueryWrapper<>();
        queryWrapper2.select("stream_consume_type as streamConsumeType, count(*) as nums ")
                .eq("stream_time_date", getNowDate())
                .eq("stream_user_id", userId)
                .groupBy("stream_consume_type");
        List<Map<String, Object>> typeList = streamDataMapper.selectMaps(queryWrapper2);

        // 异常消费占比计算
        QueryWrapper<StreamData> queryWrapper3 = new QueryWrapper<>();
        queryWrapper3
                .eq("stream_time_date", getNowDate())
                .eq("stream_user_id", userId);
        List<StreamData> streamDataList = streamDataMapper.selectList(queryWrapper3);
        int count = 0;
        for (StreamData streamData : streamDataList) {
            if (streamData.getStreamIsNormal() == 0) {
                count++;
            }
        }


        map.put("geoList", geoList);
        map.put("typeList", typeList);
        map.put("normalPercent", count / streamDataList.size() * 100);

        return map;
    }

    @Override
    public List<List<Integer>> judgeBatchByRuleGroupIds(String[] ruleGroupIds, String[] dataIds) {
        List<List<Integer>> resultList = new ArrayList<>();
        for (String ruleGroupId : ruleGroupIds) {
            List<Integer> result = this.judgeByRuleGroupId(Integer.parseInt(ruleGroupId), dataIds);
            resultList.add(result);
        }
        return resultList;
    }

    private String getNowDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date();
        String formattedDate = sdf.format(date);
        return formattedDate;
    }
}

package com.lcc.localStreamData.test;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lcc.localStreamData.entity.StreamData;
import com.lcc.localStreamData.mapper.StreamDataMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;
import java.time.LocalDateTime;
import java.util.List;

@SpringBootTest
public class MyTest {
    @Test
    public void LocalDateTimeCalculateTest() {
        LocalDateTime now = LocalDateTime.now();
        System.out.println("10 minus before:" + now.minusSeconds(60 * 10));
        System.out.println("now:" + now);
        System.out.println("30 seconds after:" + now.plusSeconds(30));
    }

    @Test
    public void SelectDatetimeBetweenTest(@Autowired StreamDataMapper streamDataMapper) {
        LocalDateTime start = LocalDateTime.of(2024, 8, 14, 18, 17, 0);
        LocalDateTime end = LocalDateTime.of(2024, 8, 14, 18, 19, 0);

        QueryWrapper<StreamData> wrapper = new QueryWrapper<>();
        wrapper.between("stream_time", start, end);
        wrapper.orderByAsc("stream_time");
        List<StreamData> streamData = streamDataMapper.selectList(wrapper);
        for (StreamData data : streamData) {
            System.err.println(data);
        }
    }
}

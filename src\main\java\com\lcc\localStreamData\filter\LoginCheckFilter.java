package com.lcc.localStreamData.filter;

import org.springframework.util.AntPathMatcher;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@WebFilter(filterName = "loginCheckFilter",urlPatterns = "/*")
public class LoginCheckFilter implements Filter {

    //url匹配器，支持通配符写法
    public static final AntPathMatcher PATH_MATCHER = new AntPathMatcher();


    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        //1.获取本次请求的URI
        String requestURI = request.getRequestURI();

        //需要直接放行的请求
        String[] urls = new String[] {
            "/login",
            "/logout",
            "/register",
            "/captcha/generate",
            "/css/**",
            "/fonts/**",
            "/images/**",
            "/js/**",
            "/plugins/**",
            "/login.html",
            "/register.html"
        };

        //2.判断本次请求是否需要处理
        boolean check = check(urls, requestURI);

        //3.如果不需要处理，则直接放行
        if (check){
            filterChain.doFilter(request,response);
            return;
        }


        Object user = request.getSession().getAttribute("user");

        //4.判断登录状态，如果已登录，则直接放行
        if (request.getSession().getAttribute("user")!=null){
            //已登录
            filterChain.doFilter(request,response);
            return;
        }

        //5.未登录则跳转到登录页面
        response.sendRedirect("/login.html");

    }

    /**
     *
     * @param urls 系统自定义的url
     * @param requestURI 收到的请求的url
     * @return
     */
    public boolean check(String[] urls,String requestURI){
        for (String url : urls) {
            boolean match = PATH_MATCHER.match(url, requestURI);
            if (match){
                return true;
            }
        }
        return false;
    }
}

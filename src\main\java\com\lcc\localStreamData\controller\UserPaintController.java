package com.lcc.localStreamData.controller;

import com.lcc.localStreamData.common.R;
import com.lcc.localStreamData.service.StreamDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
public class UserPaintController {

    @Autowired
    private StreamDataService streamDataService;

    @GetMapping("/userPaint")
    public R<Map> list(int id){

        Map map = streamDataService.userPaint(id);

        return R.success(map);
    }





}

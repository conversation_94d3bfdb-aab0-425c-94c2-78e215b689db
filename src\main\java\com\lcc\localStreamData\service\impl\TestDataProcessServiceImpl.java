package com.lcc.localStreamData.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lcc.localStreamData.entity.StreamData;
import com.lcc.localStreamData.entity.TestDataProcess;
import com.lcc.localStreamData.mapper.StreamDataMapper;
import com.lcc.localStreamData.mapper.TestDataProcessMapper;
import com.lcc.localStreamData.service.StreamDataService;
import com.lcc.localStreamData.service.TestDataProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class TestDataProcessServiceImpl extends ServiceImpl<TestDataProcessMapper, TestDataProcess> implements TestDataProcessService {

    @Autowired
    private TestDataProcessMapper testDataProcessMapper;

    @Autowired
    private StreamDataMapper streamDataMapper;

    @Autowired
    private StreamDataService streamDataService;

    private String []province={"北京市","天津市","上海市","重庆市","河北省","山西省","辽宁省","吉林省","黑龙江省","江苏省",
            "浙江省","安徽省","福建省","江西省","山东省","河南省","湖北省","湖南省","广东省","海南省",
            "四川省","贵州省","云南省","陕西省","甘肃省","青海省","台湾省","内蒙古自治区","广西壮族自治区","西藏自治区","宁夏回族自治区"
            ,"新疆维吾尔自治区","香港","澳门"};

    private String [][]city={
            {"北京市"},
            {"天津市"},
            {"上海市"},
            {"重庆市"},
            {"保定市","沧州市","邯郸市","唐山市","承德市","邢台市","石家庄市","衡水市","廊坊市","张家口市","秦皇岛市"},//河北省11个
            {"大同市","忻州市","阳泉市","长治市","晋中市","临汾市","运城市","朔州市","太原市","晋城市","吕梁市"},//山西省11个
            {"本溪市","沈阳市","朝阳市","大连市","阜新市","辽阳市","盘锦市","营口市","抚顺市","锦州市","鞍山市","丹东市","葫芦岛市","铁岭市"},//辽宁省14个
            {"长春市","白城市","辽源市","通化市","延边朝鲜族自治州","白山市","吉林市","四平市","松原市"},//吉林省9个
            {"大庆市","大兴安岭地区","牡丹江市","齐齐哈尔市","绥化市","鸡西市","双鸭山市","哈尔滨市","鹤岗市","七台河市","伊春市","佳木斯市","黑河市"},//黑龙江省13个
            {"常州市","徐州市","南京市","淮安市","南通市","宿迁市","无锡市","扬州市","盐城市","苏州市","泰州市","镇江市","连云港市"},//江苏省13个

            {"杭州市","湖州市","嘉兴市","金华市","丽水市","宁波市","衢州市","绍兴市","台州市","温州市","舟山市"},//浙江省11个
            {"合肥市","蚌埠市","阜阳市","淮北市","六安市","马鞍山市","滁州市","宿州市","淮南市","安庆市","池州市","亳州市","黄山市","宣城市","芜湖市","铜陵市"},//安徽省16个
            {"福州市","宁德市","泉州市","厦门市","莆田市","南平市","龙岩市","三明市","漳州市"},//福建省9个
            {"南昌市","赣州市","吉安市","萍乡市","宜春市","新余市","景德镇市","抚州市","九江市","上饶市","鹰潭市"},//江西省11个
            {"枣庄市","济南市","德州市","济宁市","临沂市","青岛市","泰安市","威海市","淄博市","菏泽市","烟台市","莱芜市","滨州市","东营市","聊城市","日照市","潍坊市"},//山东省17个
            {"郑州市","鹤壁市","开封市","濮阳市","三门峡市","新乡市","驻马店市","洛阳市","商丘市","济源市","南阳市","周口市","信阳市","焦作市","漯河市","平顶山市","许昌市","安阳市"},//河南省18个
            {"武汉市","荆门市","黄石市","恩施土家族苗族自治州","荆州市","十堰市","随州市","襄阳市","咸宁市","仙桃市","黄冈市","潜江市","天门市","宜昌市","神农架林区","孝感市","鄂州市"},//湖北省17个
            {"岳阳市","郴州市","衡阳市","娄底市","邵阳市","永州市","张家界市","湘西土家族苗族自治州","常德市","怀化市","湘潭市","益阳市","株洲市","长沙市"},//湖南省14个
            {"广州市","东莞市","河源市","江门市","茂名市","韶关市","云浮市","珠海市","佛山市","深圳市","梅州市","潮州市","惠州市","揭阳市","清远市","汕尾市","阳江市","肇庆市","中山市","湛江市","汕头市"},//广东省21个
            {"五指山市","文昌市","东方市","屯昌县","临高县","乐东黎族自治县","保亭黎族苗族自治县","定安县","陵水黎族自治县","儋州市","白沙黎族自治县","三亚市","琼海市","万宁市","澄迈县","昌江黎族自治县","琼中黎族苗族自治县","海口市","三沙市"},//海南省19个

            {"阿坝藏族羌族自治州","甘孜藏族自治州","内江市","自贡市","广安市","遂宁市","巴中市","德阳市","乐山市","凉山彝族自治州","眉山市","攀枝花市","雅安市","资阳市","泸州市","成都市","达州市","广元市","绵阳市","南充市","宜宾市"},//四川省21个
            {"贵阳市","六盘水市","铜仁市","黔南布依族苗族自治州","遵义市","安顺市","毕节市","黔西南布依族苗族自治州","黔东南苗族侗族自治州"},//贵州省9个
            {"昆明市","保山市","楚雄彝族自治州","大理白族自治州","德宏傣族景颇族自治州","迪庆藏族自治州","红河哈尼族彝族自治州","临沧市","丽江市","怒江傈僳族自治州","普洱市","曲靖市","文山壮族苗族自治州","西双版纳傣族自治州","玉溪市","昭通市"},//云南省16个
            {"汉中市","渭南市","延安市","榆林市","咸阳市","西安市","宝鸡市","商洛市","铜川市","安康市"},//陕西省10个
            {"兰州市","嘉峪关市","金昌市","临夏回族自治州","平凉市","武威市","定西市","张掖市","白银市","酒泉市","陇南市","天水市","庆阳市","甘南藏族自治州"},//甘肃省14个
            {"海西蒙古族藏族自治州","果洛藏族自治州","海北藏族自治州","玉树藏族自治州","黄南藏族自治州","西宁市","海东市","海南藏族自治州"},//青海省8个
            {"屏东县","宜兰县","台东县","台南县","高雄县","高雄市","云林县","苗粟县","台中县","花莲县","南投县","桃园县","新竹县","台南市","澎湖县","基隆市","台北市","台中市","嘉义县","彰化县"},//台湾省20个
            {"包头市","呼伦贝尔市","巴彦淖尔市","赤峰市","通辽市","乌海市","兴安盟","锡林郭勒盟","呼和浩特市","鄂尔多斯市","乌兰察布市","阿拉善盟"},//内蒙古自治区12个
            {"南宁市","崇左市","桂林市","贺州市","钦州市","梧州市","百色市","来宾市","河池市","北海市","防城港市","玉林市","贵港市","柳州市"},//广西壮族自治区14个
            {"阿里地区","昌都市","林芝市","那曲地区","日喀则市","山南市","拉萨市"},//西藏自治区7个

            {"中卫市","石嘴山市","吴忠市","固原市","银川市"},//宁夏回族自治区5个
            {"乌鲁木齐市","阿克苏地区","阿勒泰地区","巴音郭楞蒙古自治州","博尔塔拉蒙古自治州","昌吉回族自治州","哈密市","和田地区","克拉玛依市","克孜勒苏柯尔克孜自治州","喀什地区","石河子市","吐鲁番市","塔城地区","伊犁哈萨克自治州","阿拉尔市","图木舒克市","五家渠市","北屯市","铁门关市","可克达拉市","双河市"},//新疆维吾尔自治区22个
            {"香港"},//香港特别行政区1个
            {"澳门"},//澳门特别行政区1个

    };

    private Integer[][] type = {
            {5812,5811,5813},
            {7011,4722,5561},
            {5941,7941,7997,7933,7992},
            {5732},
            {4816,5374,7372},
            {7911,7932,5813},
            {5631,5977,7298},
            {5611},
            {5013,5021,5039,5044,5045,5046,5047,5051,5065,5074,5072,5193,5111,5122,5131,5137,5139,5172,5192,5198,5998,5398,4458},
            {5541,5542}
    };

    private String[] type_arr = {"美食","旅游","体育","电子","IT","年轻活力","女性","男性","商人","开车一族"};

    /**
     * 60秒内消费3次
     */
    @Override
    public void addDataByStrategy1() {
        //查询当前时刻的消费数据
        Date date = new Date();
        DateFormat dateFormat=new SimpleDateFormat("HH:mm");
        String condition = dateFormat.format(date);

        LambdaQueryWrapper<TestDataProcess> testDataProcessLambdaQueryWrapper = new LambdaQueryWrapper<>();
        testDataProcessLambdaQueryWrapper.likeRight(TestDataProcess::getStreamTimeMinute,condition);
        testDataProcessLambdaQueryWrapper.eq(TestDataProcess::getStreamIsNew,0);
        List<TestDataProcess> list = this.list(testDataProcessLambdaQueryWrapper);

        StreamData addData1=null;
        StreamData addData2=null;


        if (list.size()==0){
            //如果查询结果为空，则随机添加两条数据
            Random r1 = new Random();
            Long randomId1 = new Long(r1.nextInt(88018) + 854267);
            TestDataProcess data1 = this.getById(randomId1);
            Long randomId2 = new Long(r1.nextInt(88018) + 854267);
            TestDataProcess data2 = this.getById(randomId2);
            date.setTime(date.getTime() + 60*1000);//模拟未来一分钟


            addData1 = addDataInStrategy1WithSize0(data1, date);
            addData2 = addDataInStrategy1WithSize0(data2,date);


        }else if (list.size()==1){
            //如果查询结果只有一条，则只需根据这条数据进行模拟

            //模拟规则：只随机改变金额
            Random r2 = new Random();
            Integer randomMoney1 = r2.nextInt(9999182) + 1;
            Integer randomMoney2 = r2.nextInt(9999182) + 1;
            addData1 = addDataInStrategy1WithSizeGe1(list.get(0),randomMoney1);
            addData2 = addDataInStrategy1WithSizeGe1(list.get(0),randomMoney2);


        }else {
            //如果查询结果大于1条，则随机选择一条进行模拟数据
            Random r3 = new Random();
            int index = r3.nextInt(list.size()-1);//随机生成索引
            Integer randomMoney1 = r3.nextInt(9999182) + 1;
            Integer randomMoney2 = r3.nextInt(9999182) + 1;

            //模拟规则：只随机改变金额
            addData1 = addDataInStrategy1WithSizeGe1(list.get(index),randomMoney1);
            addData2 = addDataInStrategy1WithSizeGe1(list.get(index),randomMoney2);
        }

        addData1.setStreamIsNew(1);
        addData1.setStreamIsNormal(1);
        addData2.setStreamIsNew(1);
        addData2.setStreamIsNormal(1);
        streamDataService.save(addData1);
        streamDataService.save(addData2);

    }

    /**
     * 异地消费间隔，比如10分钟内，在两个不同地方消费
     */
    @Override
    public void addDataByStrategy2() {
        //查询当前时刻的消费数据
        Date date = new Date();
        DateFormat dateFormat=new SimpleDateFormat("HH:mm");
        String condition = dateFormat.format(date);

        LambdaQueryWrapper<TestDataProcess> testDataProcessLambdaQueryWrapper = new LambdaQueryWrapper<>();
        testDataProcessLambdaQueryWrapper.likeRight(TestDataProcess::getStreamTimeMinute,condition);
        testDataProcessLambdaQueryWrapper.eq(TestDataProcess::getStreamIsNew,0);
        List<TestDataProcess> list = this.list(testDataProcessLambdaQueryWrapper);

        if (list.size()!=0){
            StreamData testDataProcess = null;
            if(list.size()==1){
                testDataProcess=addDataInStrategy2(list.get(0),date);
            }else {
                Random random = new Random();
                int index = random.nextInt(list.size());
                testDataProcess=addDataInStrategy2(list.get(index),date);
            }
            testDataProcess.setStreamIsNew(1);
            testDataProcess.setStreamIsNormal(1);
            streamDataService.save(testDataProcess);
        }


    }

    /**
     * 消费频次，比如10分钟内，总共消费5次算消费异常。
     */
    @Override
    public void addDataByStrategy3() {

        Date date = new Date();
        DateFormat dateFormat=new SimpleDateFormat("HH:mm");
        String condition = dateFormat.format(date);

        LambdaQueryWrapper<TestDataProcess> testDataProcessLambdaQueryWrapper = new LambdaQueryWrapper<>();
        testDataProcessLambdaQueryWrapper.likeRight(TestDataProcess::getStreamTimeMinute,condition);
        testDataProcessLambdaQueryWrapper.eq(TestDataProcess::getStreamIsNew,0);
        List<TestDataProcess> list = this.list(testDataProcessLambdaQueryWrapper);

        StreamData addData1=null;
        StreamData addData2=null;
        StreamData addData3=null;
        StreamData addData4=null;
        if (list.size()!=0){
            if (list.size()==1){
                addData1=addDataInStrategy3(list.get(0));
                addData2=addDataInStrategy3(list.get(0));
                addData3=addDataInStrategy3(list.get(0));
                addData4=addDataInStrategy3(list.get(0));
            }else {
                Random random = new Random();
                int index = random.nextInt(list.size());
                addData1=addDataInStrategy3(list.get(index));
                addData2=addDataInStrategy3(list.get(index));
                addData3=addDataInStrategy3(list.get(index));
                addData4=addDataInStrategy3(list.get(index));
            }
            addData1.setStreamIsNew(1);
            addData1.setStreamIsNormal(1);
            addData2.setStreamIsNew(1);
            addData2.setStreamIsNormal(1);
            addData3.setStreamIsNew(1);
            addData3.setStreamIsNormal(1);
            addData4.setStreamIsNew(1);
            addData4.setStreamIsNormal(1);
            streamDataService.save(addData1);
            streamDataService.save(addData2);
            streamDataService.save(addData3);
            streamDataService.save(addData4);
        }


    }

    /**
     * 每日清空数据
     */
    @Override
    public void resetData() {
        LambdaQueryWrapper<StreamData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StreamData::getStreamTimeDate,getNowDate());
        streamDataService.remove(queryWrapper);
    }

    private String getNowDate(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date();
        String formattedDate = sdf.format(date);
        return formattedDate;
    }

    /**
     * 大屏展示-数据预览模块
     * @return
     */
    @Override
    public Map overview() {

        Map<String, Object> result = new HashMap<>();

        QueryWrapper<TestDataProcess> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("stream_time_date","20230818");
        List<TestDataProcess> testDataProcesses = testDataProcessMapper.selectList(queryWrapper);
        HashSet<Long> users = new HashSet<>();
        HashSet<String> places = new HashSet<>();

        long totalMoney = 0;
        for (TestDataProcess testDataProcess : testDataProcesses) {
            totalMoney+=testDataProcess.getStreamMoney();
            users.add(testDataProcess.getStreamUserId());
            places.add(testDataProcess.getStreamConsumeLocation());
        }

        result.put("totalMoney",totalMoney);
        result.put("users",users.size());
        result.put("places",places.size());
        result.put("totalNums",testDataProcesses.size());


        return result;
    }

    /**
     * 大屏展示-消费类型
     * @return
     */
    @Override
    public List<Map<String, Object>> type() {

        QueryWrapper<TestDataProcess> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("stream_consume_type as streamConsumeType, count(*) as nums ")
                .eq("stream_time_date","20230818")
                .groupBy("stream_consume_type");
        List<Map<String, Object>> result = testDataProcessMapper.selectMaps(queryWrapper);


        return result;
    }

    /**
     * 大屏展示-注册分布
     * @return
     */
    @Override
    public List<Map<String, Object>> sign() {

        QueryWrapper<TestDataProcess> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("stream_sign_location as streamSignLocation, count(*) as nums ")
                .eq("stream_time_date","20230818")
                .groupBy("stream_sign_location");
        List<Map<String, Object>> result = testDataProcessMapper.selectMaps(queryWrapper);

        return result;
    }

    /**
     * 全国消费统计Top13
     * @param date
     * @return
     */
    @Override
    public List<Map<String, Object>> consumeTop13(String date) {
        QueryWrapper<TestDataProcess> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("stream_sign_location, sum(stream_money) as totalMoney ")
                .eq("stream_time_date",date)
                .groupBy("stream_sign_location")
                .orderByDesc("totalMoney");
        List<Map<String, Object>> list = testDataProcessMapper.selectMaps(queryWrapper);

        ArrayList<Map<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> stringObjectMap : list) {
            if (result.size()>=13){
                break;
            }
            result.add(stringObjectMap);
        }

        return result;
    }

    /**
     * 全国热销
     * @param date
     * @return
     */
    @Override
    public List<Map> nationTop(String date) {

        QueryWrapper<TestDataProcess> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("stream_consume_type as streamConsumeType, count(*) as nums ")
                .eq("stream_time_date",date)
                .groupBy("stream_consume_type");
        List<Map<String, Object>> result = testDataProcessMapper.selectMaps(queryWrapper);
        return countType1(result);
    }

    /**
     * 销售额统计
     * @param date
     * @param time
     * @return
     */
    @Override
    public long[] line(String date, String time) {

        QueryWrapper<TestDataProcess> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("stream_time_date",date);
        long[] res = {0,0,0,0,0,0,0,0};
        if (time.equals("morning")){
            queryWrapper.between("stream_seconds",0,28800);
            List<TestDataProcess> list = testDataProcessMapper.selectList(queryWrapper);
            addMoneyByTime(list,res,0);

        }else if (time.equals("noon")){
            queryWrapper.between("stream_seconds",28801,57600);
            List<TestDataProcess> list = testDataProcessMapper.selectList(queryWrapper);
            addMoneyByTime(list,res,28800);

        }else {
            queryWrapper.between("stream_seconds",57601,86400);
            List<TestDataProcess> list = testDataProcessMapper.selectList(queryWrapper);
            addMoneyByTime(list,res,57600);

        }

        return res;
    }

    /**
     * 查询销量前5的省级列表
     * @param date
     * @return
     */
    public List<Map> hotProvince(String date) {

        QueryWrapper<TestDataProcess> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("stream_sign_location as city, sum(stream_money) as sales ")
                .eq("stream_time_date",date)
                .groupBy("stream_sign_location")
                .orderByDesc("sales");
        List<Map<String, Object>> list = testDataProcessMapper.selectMaps(queryWrapper);

        ArrayList<Map> result = new ArrayList<>();
        for (Map stringObjectMap : list) {
            if (result.size()>=5){
                break;
            }
            result.add(stringObjectMap);
        }
        return result;
    }

    /**
     * 根据省份名称查询对应的至多前5的商品销量数据
     * @param date
     * @param place
     * @return
     */
    public List<Map> hotSale(String date,String place) {

        QueryWrapper<TestDataProcess> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("stream_consume_type as name, sum(stream_money) as num ")
                .eq("stream_time_date",date)
                .eq("stream_sign_location",place)
                .groupBy("stream_consume_type")
                .orderByDesc("num");
        List<Map<String, Object>> list = testDataProcessMapper.selectMaps(queryWrapper);
        ArrayList<Map<String, Object>> result1 = new ArrayList<>();
        for (Map stringObjectMap : list) {
            if (result1.size()>=5){
                break;
            }
            result1.add(stringObjectMap);
        }

        ArrayList<Map> result2 = countType2(result1);

        return result2;
    }

    public void addMoneyByTime(List<TestDataProcess> list,long[] res,int base){
        for (TestDataProcess testDataProcess : list) {
            Integer streamSeconds = testDataProcess.getStreamSeconds();
            if (streamSeconds >=(0+base)&& streamSeconds <=(3600+base)){
                res[0]+=testDataProcess.getStreamMoney();
            }else if (streamSeconds >=(3601+base)&& streamSeconds <=(7200+base)){
                res[1]+=testDataProcess.getStreamMoney();
            } else if (streamSeconds >=(7201+base)&& streamSeconds <=(10800+base)){
                res[2]+=testDataProcess.getStreamMoney();
            } else if (streamSeconds >=(10801+base)&& streamSeconds <=(14400+base)){
                res[3]+=testDataProcess.getStreamMoney();
            } else if (streamSeconds >=(14401+base)&& streamSeconds <=(18000+base)){
                res[4]+=testDataProcess.getStreamMoney();
            }else if (streamSeconds >=(18001+base)&& streamSeconds <=(21600+base)){
                res[5]+=testDataProcess.getStreamMoney();
            } else if (streamSeconds >=(21601+base)&& streamSeconds <=(25200+base)){
                res[6]+=testDataProcess.getStreamMoney();
            } else{
                res[7]+=testDataProcess.getStreamMoney();
            }
        }
    }


    public ArrayList<Map> countType1(List<Map<String, Object>> result){

        ArrayList<Map> list = new ArrayList<>();

        HashMap<String, Object> hashMap = new HashMap<>();
        ArrayList<String> places = new ArrayList<>();

        for (int i = 0; i < result.size(); i++) {
            Integer num = (Integer) result.get(i).get("streamConsumeType");//获取消费类型编号

            int index=-1;
            for (int j = 0; j < type.length; j++) {
                if (Arrays.asList(type[j]).contains(num)){
                    index=j;
                    break;
                }
            }

            int index1 = places.indexOf(type_arr[index]);
            if (index1!=-1){
                //若已添加过该消费类型，则只需累加数量
                list.get(index1).put("value",(Long)list.get(index1).get("value")+(Long)result.get(i).get("nums"));

            }else {
                HashMap<String, Object> map = new HashMap<>();
                map.put("name",type_arr[index]);
                map.put("value",result.get(i).get("nums"));
                list.add(map);
                places.add(type_arr[index]);
            }

        }

        return list;
    }

    public ArrayList<Map> countType2(List<Map<String, Object>> result){

        ArrayList<Map> list = new ArrayList<>();

        ArrayList<String> places = new ArrayList<>();

        for (int i = 0; i < result.size(); i++) {
            Integer num = (Integer) result.get(i).get("name");//获取消费类型编号

            int index=-1;
            for (int j = 0; j < type.length; j++) {
                if (Arrays.asList(type[j]).contains(num)){
                    index=j;
                    break;
                }
            }

            int index1 = places.indexOf(type_arr[index]);
            if (index1!=-1){
                //若已添加过该消费类型，则只需累加数量
                list.get(index1).put("num",((BigDecimal)list.get(index1).get("num")).add((BigDecimal)result.get(i).get("num")));

            }else {
                HashMap<String, Object> map = new HashMap<>();
                map.put("name",type_arr[index]);
                map.put("num",result.get(i).get("num"));
                list.add(map);
                places.add(type_arr[index]);
            }

        }

        return list;
    }


    public Integer timeToSeconds(LocalDateTime localDateTime){
        return localDateTime.getHour()*3600+localDateTime.getMinute()*60+localDateTime.getSecond();
    }

    public StreamData addDataInStrategy1WithSize0(TestDataProcess parent, Date date){
        StreamData testDataProcess = new StreamData();


        //设置未来一分钟的时间用于绑定给新模拟的数据
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int YEAR = calendar.get(Calendar.YEAR);
        int MONTH = calendar.get(Calendar.MONTH)+1;
        int DAY_OF_MONTH = calendar.get(Calendar.DAY_OF_MONTH);
        int HOUR_OF_DAY = calendar.get(Calendar.HOUR_OF_DAY);
        int MINUTE = calendar.get(Calendar.MINUTE);
        int SECOND = calendar.get(Calendar.SECOND);
        LocalDateTime localDateTime = LocalDateTime.of(YEAR, MONTH, DAY_OF_MONTH, HOUR_OF_DAY, MINUTE, SECOND);

        testDataProcess.setStreamTime(localDateTime);
        testDataProcess.setStreamUserId(parent.getStreamUserId());
        testDataProcess.setStreamConsumeType(parent.getStreamConsumeType());
        testDataProcess.setStreamConsumeLocation(parent.getStreamConsumeLocation());
        testDataProcess.setStreamSignLocation(parent.getStreamSignLocation());
        testDataProcess.setStreamMoney(parent.getStreamMoney());

        //声明需要格式化的格式(日期)  --> 20150501
        DateTimeFormatter dfDate1 = DateTimeFormatter.ofPattern("yyyyMMdd");
        String format1 = dfDate1.format(localDateTime);
        testDataProcess.setStreamTimeDate(Integer.parseInt(format1));

        //声明需要格式化的格式(日期加时间)
        DateTimeFormatter dfDate2 = DateTimeFormatter.ofPattern("HH:mm:ss");
        String format2 = dfDate2.format(localDateTime);
        testDataProcess.setStreamTimeMinute(format2);
        testDataProcess.setStreamSeconds(timeToSeconds(testDataProcess.getStreamTime()));
        return testDataProcess;
    }

    public StreamData addDataInStrategy1WithSizeGe1(TestDataProcess parent, Integer randomMoney){
        StreamData testDataProcess = new StreamData();
        //只随机改变金额
        testDataProcess.setStreamMoney(randomMoney);
        Date date = new Date();
        Random r = new Random();
        int randomTime = r.nextInt(60);

        date.setTime(date.getTime() + 1000*randomTime);

        //设置未来一分钟的时间用于绑定给新模拟的数据
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int YEAR = calendar.get(Calendar.YEAR);
        int MONTH = calendar.get(Calendar.MONTH)+1;
        int DAY_OF_MONTH = calendar.get(Calendar.DAY_OF_MONTH);
        int HOUR_OF_DAY = calendar.get(Calendar.HOUR_OF_DAY);
        int MINUTE = calendar.get(Calendar.MINUTE);
        int SECOND = calendar.get(Calendar.SECOND);
        LocalDateTime localDateTime = LocalDateTime.of(YEAR, MONTH, DAY_OF_MONTH, HOUR_OF_DAY, MINUTE, SECOND);

        testDataProcess.setStreamTime(localDateTime);
        testDataProcess.setStreamUserId(parent.getStreamUserId());
        testDataProcess.setStreamConsumeType(parent.getStreamConsumeType());
        testDataProcess.setStreamConsumeLocation(parent.getStreamConsumeLocation());
        testDataProcess.setStreamSignLocation(parent.getStreamSignLocation());

        //声明需要格式化的格式(日期)  --> 20150501
        DateTimeFormatter dfDate1 = DateTimeFormatter.ofPattern("yyyyMMdd");
        String format1 = dfDate1.format(localDateTime);
        testDataProcess.setStreamTimeDate(Integer.parseInt(format1));

        //声明需要格式化的格式(日期加时间)
        DateTimeFormatter dfDate2 = DateTimeFormatter.ofPattern("HH:mm:ss");
        String format2 = dfDate2.format(localDateTime);
        testDataProcess.setStreamTimeMinute(format2);
        testDataProcess.setStreamSeconds(timeToSeconds(testDataProcess.getStreamTime()));
        return testDataProcess;
    }

    public StreamData addDataInStrategy2(TestDataProcess parent, Date date){
        StreamData testDataProcess = new StreamData();

        Random r = new Random();


        int randomTime = r.nextInt(5) + 5;

        date.setTime(date.getTime() + 60*1000*randomTime);


        //设置未来一分钟的时间用于绑定给新模拟的数据
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int YEAR = calendar.get(Calendar.YEAR);
        int MONTH = calendar.get(Calendar.MONTH)+1;
        int DAY_OF_MONTH = calendar.get(Calendar.DAY_OF_MONTH);
        int HOUR_OF_DAY = calendar.get(Calendar.HOUR_OF_DAY);
        int MINUTE = calendar.get(Calendar.MINUTE);
        int SECOND = calendar.get(Calendar.SECOND);
        LocalDateTime localDateTime = LocalDateTime.of(YEAR, MONTH, DAY_OF_MONTH, HOUR_OF_DAY, MINUTE, SECOND);

        testDataProcess.setStreamTime(localDateTime);
        testDataProcess.setStreamUserId(parent.getStreamUserId());
        testDataProcess.setStreamConsumeType(parent.getStreamConsumeType());
        testDataProcess.setStreamSignLocation(parent.getStreamSignLocation());
        testDataProcess.setStreamMoney(r.nextInt(9999182) + 1);//随机改变消费金额

        int p_index = r.nextInt(34);//生成省的随机索引
        int c_index = r.nextInt(city[p_index].length);//生成对应省的市随机索引
        String randomLocation = province[p_index]+","+city[p_index][c_index];
        testDataProcess.setStreamConsumeLocation(randomLocation);//随机改变消费地点

        //声明需要格式化的格式(日期)  --> 20150501
        DateTimeFormatter dfDate1 = DateTimeFormatter.ofPattern("yyyyMMdd");
        String format1 = dfDate1.format(localDateTime);
        testDataProcess.setStreamTimeDate(Integer.parseInt(format1));

        //声明需要格式化的格式(日期加时间)
        DateTimeFormatter dfDate2 = DateTimeFormatter.ofPattern("HH:mm:ss");
        String format2 = dfDate2.format(localDateTime);
        testDataProcess.setStreamTimeMinute(format2);
        testDataProcess.setStreamSeconds(timeToSeconds(testDataProcess.getStreamTime()));
        return testDataProcess;
    }

    public StreamData addDataInStrategy3(TestDataProcess parent){
        StreamData testDataProcess = new StreamData();

        Random r = new Random();

        Date date = new Date();


        int randomTime = r.nextInt(5) + 5;

        date.setTime(date.getTime() + 60*1000*randomTime);


        //设置未来一分钟的时间用于绑定给新模拟的数据
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int YEAR = calendar.get(Calendar.YEAR);
        int MONTH = calendar.get(Calendar.MONTH)+1;
        int DAY_OF_MONTH = calendar.get(Calendar.DAY_OF_MONTH);
        int HOUR_OF_DAY = calendar.get(Calendar.HOUR_OF_DAY);
        int MINUTE = calendar.get(Calendar.MINUTE);
        int SECOND = calendar.get(Calendar.SECOND);
        LocalDateTime localDateTime = LocalDateTime.of(YEAR, MONTH, DAY_OF_MONTH, HOUR_OF_DAY, MINUTE, SECOND);

        testDataProcess.setStreamTime(localDateTime);
        testDataProcess.setStreamUserId(parent.getStreamUserId());
        testDataProcess.setStreamConsumeType(parent.getStreamConsumeType());
        testDataProcess.setStreamConsumeLocation(parent.getStreamConsumeLocation());
        testDataProcess.setStreamSignLocation(parent.getStreamSignLocation());
        testDataProcess.setStreamMoney(r.nextInt(9999182) + 1);//随机改变消费金额

        //声明需要格式化的格式(日期)  --> 20150501
        DateTimeFormatter dfDate1 = DateTimeFormatter.ofPattern("yyyyMMdd");
        String format1 = dfDate1.format(localDateTime);
        testDataProcess.setStreamTimeDate(Integer.parseInt(format1));

        //声明需要格式化的格式(日期加时间)
        DateTimeFormatter dfDate2 = DateTimeFormatter.ofPattern("HH:mm:ss");
        String format2 = dfDate2.format(localDateTime);
        testDataProcess.setStreamTimeMinute(format2);
        testDataProcess.setStreamSeconds(timeToSeconds(testDataProcess.getStreamTime()));
        return testDataProcess;
    }
}

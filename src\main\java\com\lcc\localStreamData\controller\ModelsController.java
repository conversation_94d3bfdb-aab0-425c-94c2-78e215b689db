package com.lcc.localStreamData.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcc.localStreamData.common.R;
import com.lcc.localStreamData.entity.Models;
import com.lcc.localStreamData.service.ModelsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/models")
public class ModelsController {

    @Autowired
    private ModelsService modelsService;

    @GetMapping("/list")
    public R<List<Models>> list() {
        List<Models> list = modelsService.list();
        return R.success(list);
    }

    @GetMapping("/list/admin")
    public R<Page<Models>> listAdmin(int page, int pageSize, String trainer) {
        Page<Models> list = modelsService.findListAdmin(page, pageSize, trainer);
        return R.success(list);
    }

    @GetMapping("/list/user")
    public R<Page<Models>> listUser(int page, int pageSize, Integer trainerId) {
        Page<Models> list = modelsService.findListUser(page, pageSize, trainerId);
        return R.success(list);
    }


}

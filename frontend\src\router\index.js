import { createRouter, createWebHistory } from 'vue-router'
import Login from '../views/Login.vue'
import Layout from '../views/Layout.vue'
import Dashboard from '../views/Dashboard.vue'
import UserManagement from '../views/UserManagement.vue'
import DataManagement from '../views/DataManagement.vue'
import DatasetManagement from '../views/DatasetManagement.vue'
import DataProcess from '../views/DataProcess.vue'
import RuleManagement from '../views/RuleManagement.vue'
import ModelAdmin from '../views/ModelAdmin.vue'
import ModelUser from '../views/ModelUser.vue'
import RiskRule from '../views/RiskRule.vue'
import RiskModel from '../views/RiskModel.vue'
import RiskSelfModel from '../views/RiskSelfModel.vue'
import RiskFusion from '../views/RiskFusion.vue'
import UserAlert from '../views/UserAlert.vue'
import Register from '../views/Register.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/register',
    name: 'Register',
    component: Register
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
    
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: { title: 'Dashboard' }
      },
      {
        path: 'user-management',
        name: 'UserManagement',
        component: UserManagement,
        meta: { title: '用户管理', requiresAdmin: true }
      },
      // 数据管理&预处理 - 嵌套路由
      {
        path: 'data',
        name: 'DataModule',
        component: () => import('../views/modules/DataModule.vue'),
        meta: { title: '数据管理&预处理' },
        redirect: '/data/management',
        children: [
          {
            path: 'management',
            name: 'DataManagement',
            component: DataManagement,
            meta: { title: '流数据管理' }
          },
          {
            path: 'dataset',
            name: 'DatasetManagement',
            component: DatasetManagement,
            meta: { title: '数据集管理' }
          },
          {
            path: 'process',
            name: 'DataProcess',
            component: DataProcess,
            meta: { title: '数据预处理' }
          }
        ]
      },
      // 模型管理 - 嵌套路由
      {
        path: 'model',
        name: 'ModelModule',
        component: () => import('../views/modules/ModelModule.vue'),
        meta: { title: '模型管理' },
        redirect: '/model/rules',
        children: [
          {
            path: 'rules',
            name: 'RuleManagement',
            component: RuleManagement,
            meta: { title: '规则管理' }
          },
          {
            path: 'training-admin',
            name: 'ModelAdmin',
            component: ModelAdmin,
            meta: { title: '模型训练', requiresAdmin: true }
          },
          {
            path: 'training-user',
            name: 'ModelUser',
            component: ModelUser,
            meta: { title: '模型训练' }
          }
        ]
      },
      // 风险评估 - 嵌套路由
      {
        path: 'risk',
        name: 'RiskModule',
        component: () => import('../views/modules/RiskModule.vue'),
        meta: { title: '风险评估' },
        redirect: '/risk/rule',
        children: [
          {
            path: 'rule',
            name: 'RiskRule',
            component: RiskRule,
            meta: { title: '规则评估' }
          },
          {
            path: 'model',
            name: 'RiskModel',
            component: RiskModel,
            meta: { title: '平台模型评估' }
          },
          {
            path: 'selfmodel',
            name: 'RiskSelfModel',
            component: RiskSelfModel,
            meta: { title: '自训练模型评估' }
          },
          {
            path: 'fusion',
            name: 'RiskFusion',
            component: RiskFusion,
            meta: { title: '规则+模型融合评估' }
          }
        ]
      },
      {
        path: 'user-alert',
        name: 'UserAlert',
        component: UserAlert,
        meta: { title: '用户预警' }
      },
      // 金融欺诈流数据模拟 - 嵌套路由
      {
        path: 'fraud-simulation',
        name: 'FraudSimulationModule',
        component: () => import('../views/modules/FraudSimulationModule.vue'),
        meta: { title: '金融欺诈流数据模拟' },
        redirect: '/fraud-simulation/simulation',
        children: [
          {
            path: 'simulation',
            name: 'FraudSimulation',
            component: () => import('../views/FraudSimulation.vue'),
            meta: { title: '金融欺诈流数据模拟' }
          },
          {
            path: 'stream-generation',
            name: 'StreamGeneration',
            component: () => import('../views/StreamGeneration.vue'),
            meta: { title: '流数据生成与处理' }
          }
        ]
      },

      // 金融欺诈实时数据分析 - 嵌套路由
      {
        path: 'real-time-analysis',
        name: 'RealTimeAnalysisModule',
        component: () => import('../views/modules/RealTimeAnalysisModule.vue'),
        meta: { title: '金融欺诈实时数据分析' },
        redirect: '/real-time-analysis/preprocessing',
        children: [
          {
            path: 'preprocessing',
            name: 'StreamPreprocessing',
            component: () => import('../views/StreamPreprocessing.vue'),
            meta: { title: '流数据预处理' }
          },
          {
            path: 'rule-training',
            name: 'RiskRuleTraining',
            component: () => import('../views/RiskRuleTraining.vue'),
            meta: { title: '风险评估规则/决策训练' }
          },
          {
            path: 'model-deployment',
            name: 'ModelDeployment',
            component: () => import('../views/ModelDeployment.vue'),
            meta: { title: '模型发布与决策训练' }
          },
          {
            path: 'assessment',
            name: 'RealTimeAssessment',
            component: () => import('../views/RealTimeAssessment.vue'),
            meta: { title: '风险实时评估与预警' }
          }
        ]
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userInfo = localStorage.getItem('userInfo')

  if (to.path === '/login' || to.path === '/register') {
    next()
  } else if (!userInfo) {
    next('/login')
  } else {
    const user = JSON.parse(userInfo)
    if (to.meta.requiresAdmin && user.userLevel !== '1') {
      // 如果需要管理员权限但用户不是管理员，重定向到dashboard
      next('/dashboard')
    } else {
      next()
    }
  }
})

export default router

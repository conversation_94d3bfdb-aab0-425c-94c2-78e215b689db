<template>
  <div class="risk-module">
    <el-card>
      <template #header>
        <div class="module-header">
          <h2>风险评估</h2>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>风险评估</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </template>

      <!-- 子模块导航 -->
      <div class="sub-navigation">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="规则评估" name="rule">
            <template #label>
              <span class="tab-label">
                <el-icon><DocumentChecked /></el-icon>
                规则评估
              </span>
            </template>
          </el-tab-pane>
          <el-tab-pane label="平台模型评估" name="model">
            <template #label>
              <span class="tab-label">
                <el-icon><Cpu /></el-icon>
                平台模型评估
              </span>
            </template>
          </el-tab-pane>
          <el-tab-pane label="自训练模型评估" name="selfmodel">
            <template #label>
              <span class="tab-label">
                <el-icon><Setting /></el-icon>
                自训练模型评估
              </span>
            </template>
          </el-tab-pane>
          <el-tab-pane label="规则+模型融合评估" name="fusion">
            <template #label>
              <span class="tab-label">
                <el-icon><Connection /></el-icon>
                规则+模型融合评估
              </span>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 子页面内容 -->
      <div class="module-content">
        <router-view />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { DocumentChecked, Cpu, Setting, Connection } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const activeTab = ref('rule')

// 根据当前路由设置活跃标签
const setActiveTabFromRoute = () => {
  const routeName = route.name
  switch (routeName) {
    case 'RiskRule':
      activeTab.value = 'rule'
      break
    case 'RiskModel':
      activeTab.value = 'model'
      break
    case 'RiskSelfModel':
      activeTab.value = 'selfmodel'
      break
    case 'RiskFusion':
      activeTab.value = 'fusion'
      break
    default:
      activeTab.value = 'rule'
  }
}

// 当前页面标题
const currentPageTitle = computed(() => {
  switch (activeTab.value) {
    case 'rule':
      return '规则评估'
    case 'model':
      return '平台模型评估'
    case 'selfmodel':
      return '自训练模型评估'
    case 'fusion':
      return '规则+模型融合评估'
    default:
      return '规则评估'
  }
})

// 标签切换处理
const handleTabChange = (tabName) => {
  switch (tabName) {
    case 'rule':
      router.push('/risk/rule')
      break
    case 'model':
      router.push('/risk/model')
      break
    case 'selfmodel':
      router.push('/risk/selfmodel')
      break
    case 'fusion':
      router.push('/risk/fusion')
      break
  }
}

// 监听路由变化
watch(() => route.name, () => {
  setActiveTabFromRoute()
}, { immediate: true })
</script>

<style scoped>
.risk-module {
  height: 100%;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.module-header h2 {
  margin: 0;
  color: #333;
}

.sub-navigation {
  margin-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.module-content {
  min-height: 500px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-tabs__header) {
  margin: 0 0 20px 0;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__item) {
  padding: 0 20px;
  height: 50px;
  line-height: 50px;
  font-size: 14px;
}

:deep(.el-tabs__item.is-active) {
  color: #409EFF;
  font-weight: 600;
}
</style>

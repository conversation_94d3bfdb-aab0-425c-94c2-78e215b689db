package com.lcc.localStreamData.controller;

import com.lcc.localStreamData.common.R;
import com.lcc.localStreamData.entity.NewData;
import com.lcc.localStreamData.entity.TestData;
import com.lcc.localStreamData.entity.TestDataProcess;
import com.lcc.localStreamData.service.NewDataService;
import com.lcc.localStreamData.service.TestDataProcessService;
import com.lcc.localStreamData.service.TestDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Random;

@Slf4j
@RestController
@RequestMapping("/newData")
public class NewDataController {

    @Autowired
    private NewDataService newDataService;



    @PostMapping("/save")
    public R<String> save(){


        newDataService.addDataByStrategy1();

        return R.success("操作成功");
    }



}

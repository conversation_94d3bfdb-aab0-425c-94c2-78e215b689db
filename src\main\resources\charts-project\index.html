<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="fonts/icomoon.css">
    <link rel="stylesheet" href="css/index.css">
    <script src="js/echarts.min.js"></script>
    <script src="js/flexible.js"></script>
    <script src="js/jquery.min.js"></script>
    <script src="js/china.js"></script>
    <style>
        .overview .inner span {
            font-size: 14px;
        }

        .data .item span {
            font-size: 15px;
        }
    </style>
</head>
<body>
    <div class="viewport" id="app">
        <div class="column">
            <div class="panel overview">
                <div class="inner">
                    <ul>
                        <li>
                            <h4>21222</h4>
                            <span>
                                <i class="icon-dot"></i>
                                消费金额(元)
                            </span>
                        </li>
                        <li class="item">
                            <h4>2000</h4>
                            <span>
                                <i class="icon-dot" style="color: #6acca3"></i>
                                消费人数
                            </span>
                        </li>
                        <li>
                            <h4>1573</h4>
                            <span>
                                <i class="icon-dot" style="color: #6acca3"></i>
                                消费地点
                            </span>
                        </li>
                        <li>
                            <h4>18888</h4>
                            <span>
                                <i class="icon-dot" style="color: #ed3f35"></i>
                                消费总次数
                            </span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="panel monitor">
                <div class="inner">
                    <div class="tabs">
                        <a href="javascript:;" class="active">异常消费监控</a>
                    </div>
                    <div class="content" style="display: block">
                        <div class="head">
                            <span class="col">消费时间</span>
                            <span class="col">消费地点</span>
                            <span class="col">消费金额(元)</span>
                        </div>
                        <div class="marquee-view">
                            <div class="marquee">
                                <div class="row">
                                    <span class="col">20180701</span>
                                    <span class="col">11北京市昌平西路金燕龙写字楼</span>
                                    <span class="col">1000001</span>
                                    <span class="icon-dot"></span>
                                </div>
                                <div class="row">
                                    <span class="col">20190601</span>
                                    <span class="col">北京市昌平区城西路金燕龙写字楼</span>
                                    <span class="col">1000002</span>
                                    <span class="icon-dot"></span>
                                </div>
                                <div class="row">
                                    <span class="col">20190704</span>
                                    <span class="col">北京市昌平区建材城西路金燕龙写字楼</span>
                                    <span class="col">1000003</span>
                                    <span class="icon-dot"></span>
                                </div>
                                <div class="row">
                                    <span class="col">20180701</span>
                                    <span class="col">北京市昌平区建路金燕龙写字楼</span>
                                    <span class="col">1000004</span>
                                    <span class="icon-dot"></span>
                                </div>
                                <div class="row">
                                    <span class="col">20190701</span>
                                    <span class="col">北京市昌平区建材城西路金燕龙写字楼</span>
                                    <span class="col">1000005</span>
                                    <span class="icon-dot"></span>
                                </div>
                                <div class="row">
                                    <span class="col">20190701</span>
                                    <span class="col">北京市昌平区建材城西路金燕龙写字楼</span>
                                    <span class="col">1000006</span>
                                    <span class="icon-dot"></span>
                                </div>
                                <div class="row">
                                    <span class="col">20190701</span>
                                    <span class="col">北京市昌平区建西路金燕龙写字楼</span>
                                    <span class="col">1000007</span>
                                    <span class="icon-dot"></span>
                                </div>
                                <div class="row">
                                    <span class="col">20190701</span>
                                    <span class="col">北京市昌平区建材城西路金燕龙写字楼</span>
                                    <span class="col">1000008</span>
                                    <span class="icon-dot"></span>
                                </div>
                                <div class="row">
                                    <span class="col">20190701</span>
                                    <span class="col">北京市昌平区建材城西路金燕龙写字楼</span>
                                    <span class="col">1000009</span>
                                    <span class="icon-dot"></span>
                                </div>
                                <div class="row">
                                    <span class="col">20190710</span>
                                    <span class="col">北京市昌平区建材城西路金燕龙写字楼</span>
                                    <span class="col">1000010</span>
                                    <span class="icon-dot"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 点位 -->
            <div class="point panel">
                <div class="inner">
                    <h3>消费类型统计</h3>
                    <div class="chart">
                        <div class="pie"></div>
                        <div class="data">
                            <div class="item">
                                <h4>320,11</h4>
                                <span>
                                      <i class="icon-dot" style="color: #ed3f35"></i>
                                      点位总数
                                </span>
                            </div>
                            <div class="item">
                                <h4>418</h4>
                                <span>
                                      <i class="icon-dot" style="color: #eacf19"></i>
                                      本月新增
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="column">
            <!-- 地图 -->
            <div class="map">
                <h3>
                    <span class="icon-cube"></span>
                    异常消费分布
                </h3>
                <div class="chart">
                    <div class="geo"></div>
                </div>
            </div>
            <!-- 用户 -->
            <div class="users panel">
                <div class="inner">
                    <h3>全国消费总量(Top13)</h3>
                    <div class="chart">
                        <div class="bar"></div>
                        <div class="data">
                            <div class="item">
                                <h4>120,899</h4>
                                <span>
                                  <i class="icon-dot" style="color: #ed3f35"></i>
                                  消费总量
                                </span>
                            </div>
                            <div class="item">
                                <h4>248</h4>
                                <span>
                                  <i class="icon-dot" style="color: #eacf19"></i>
                                  本月新增
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="column">
            <!-- 订单 -->
            <div class="order panel">
                <div class="inner">
                    <!-- 筛选 -->
                    <div class="filter">
                        <a href="javascript:;"  class="active">365天</a>
                        <a href="javascript:;" >90天</a>
                        <a href="javascript:;" >30天</a>
                        <a href="javascript:;" >24小时</a>
                    </div>
                    <!-- 数据 -->
                    <div class="data">
                        <div class="item">
                            <h4>20,301,987</h4>
                            <span>
                                <i class="icon-dot" style="color: #ed3f35;"></i>
                                订单量
                            </span>
                        </div>
                        <div class="item">
                            <h4>99834</h4>
                            <span>
                                <i class="icon-dot" style="color: #eacf19;"></i>
                                销售额(万元)
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 销售额 -->
            <div class="sales panel">
                <div class="inner">
                    <div class="caption">
                        <h3>销售额统计</h3>
                        <a href="javascript:;" class="active" data-type="morning">早</a>
                        <a href="javascript:;" data-type="noon">中</a>
                        <a href="javascript:;" data-type="night">晚</a>
                    </div>
                    <div class="chart">
                        <div class="label">单位:万</div>
                        <div class="line"></div>
                    </div>
                </div>
            </div>
            <!-- 渠道 季度 -->
            <div class="wrap">
                <div class="channel panel">
                    <div class="inner">
                        <h3>注册地分布</h3>
                        <div class="data">
                            <div class="radar"></div>
                        </div>
                    </div>
                </div>
                <div class="quarter panel">
                    <div class="inner">
                        <h3>异常消费占比</h3>
                        <div class="chart">
                            <div class="box">
                                <div class="gauge"></div>
                                <div class="label">75<small> %</small></div>
                            </div>
                            <div class="data">
                                <div class="item">
                                    <h4>1,321</h4>
                                    <span>
                                      <i class="icon-dot" style="color: #6acca3"></i>
                                      总额(万元)
                                    </span>
                                </div>
                                <div class="item">
                                    <h4>150%</h4>
                                    <span>
                                      <i class="icon-dot" style="color: #ed3f35"></i>
                                      同比增长
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 排行榜 -->
            <div class="top panel">
                <div class="inner">
                    <div class="all">
                        <h3>全国热榜</h3>
                        <ul>
                            <li>
                                <i class="icon-cup1" style="color: #d93f36;"></i>
                                可爱多
                            </li>
                            <li>
                                <i class="icon-cup2" style="color: #68d8fe;"></i>
                                娃哈啥
                            </li>
                            <li>
                                <i class="icon-cup3" style="color: #4c9bfd;"></i>
                                喜之郎
                            </li>
                        </ul>
                    </div>
                    <div class="province">
                        <h3>各省热销 </h3>
                        <div class="data">
                            <ul class="sup">
<!--                                <li>-->
<!--                                    <span>北京</span>-->
<!--                                    <span>25,179 <s class="icon-up"></s></span>-->
<!--                                </li>-->
                            </ul>
                            <ul class="sub">
                                <!-- <li><span></span><span> <s class="icon-up"></s></span></li> -->
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        let app = new Vue({
            el: "#app",
            data(){
                return {
                    overView: {
                        places:0,
                        totalMoney:0,
                        totalNums:0,
                        users:0
                    }
                }
            }
        })
    </script>
    <script>

        // 动画
        $(".marquee-view .marquee").each(function() {
            // console.log($(this));
            var rows = $(this).children().clone();
            $(this).append(rows);
        });

        // 饼图部分
        var pieChart = echarts.init(document.querySelector(".pie"));
        var pie_option = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b} : {c} ({d}%)'
            },
            color: ['#006cff', '#60cda0', '#ed8884', '#ff9f7f', '#0096ff', '#9fe6b8', '#32c5e9', '#1d9dff'],
            series: [
                {
                    name: 'Area Mode',
                    type: 'pie',
                    radius: ["10%", "70%"],
                    center: ['50%', '50%'],
                    roseType: 'radius',
                    data: [
                        { value: 20, name: '美食' },
                        { value: 26, name: '旅游' },
                        { value: 24, name: '体育' },
                        { value: 25, name: '电子' },
                        { value: 20, name: 'IT' },
                        { value: 25, name: '年轻活力' },
                        { value: 30, name: '商人' },
                        { value: 42, name: '开车一族' }
                    ],
                    label: {
                        fontSize: 10
                    },
                    labelLine: {
                        length: 6,
                        length2: 8
                    }
                }
            ]
        };
        pieChart.setOption(pie_option);
        // 设置图表可随着浏览器大小等比例缩放调整
        window.addEventListener("resize", function () {
            pieChart.resize();
        })


        var barChart = echarts.init(document.querySelector(".bar"));
        var bar_option = {
            color: new echarts.graphic.LinearGradient(
                // (x1,y2) 点到点 (x2,y2) 之间进行渐变
                0, 0, 0, 1,
                [
                    { offset: 0, color: '#00fffb' }, // 0 起始颜色
                    { offset: 1, color: '#0061ce' }  // 1 结束颜色
                ]
            ),
            tooltip: {
                trigger: 'item',
                // axisPointer: {
                //     type: 'shadow'
                // }
            },
            grid: {
                left: '0%',
                right: '3%',
                bottom: '3%',
                top: '3%',
                containLabel: true,
                // 是否显示直角坐标系网格
                show: true,
                //grid 四条边框的颜色
                borderColor: 'rgba(71,229,239,0.3)'
            },
            xAxis: [
                {
                    type: 'category',
                    data: ['上海', '广州', '北京', '深圳', '合肥', '', '......', '', '杭州', '厦门', '济南', '成都', '重庆'],
                    axisTick: {
                        alignWithLabel: false,
                        show: false
                    },
                    axisLabel: {
                        color: "#4c9bfd"
                    },
                    axisLine: {
                        lineStyle: {
                            color: "rgba(71,229,239,0.3)"
                        }
                    }
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    axisTick: {
                        alignWithLabel: false,
                        show: false
                    },
                    axisLabel: {
                        color: "#4c9bfd"
                    },
                    axisLine: {
                        lineStyle: {
                            color: "rgba(71,229,239,0.3)"
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: "rgba(71,229,239,0.3)"
                        }
                    }
                }
            ],
            series: [
                {
                    name: '直接访问',
                    type: 'bar',
                    barWidth: '60%',
                    data: [2100,1900,1700,1560,1400,1200,1200,1200,900,750,600,480,240]
                }
            ]
        };
        barChart.setOption(bar_option);
        // 设置图表可随着浏览器大小等比例缩放调整
        window.addEventListener("resize", function () {
            barChart.resize();
        })

        var lineChart = echarts.init(document.querySelector(".line"))

        var line_data = {
            morning: [24, 40, 101, 134, 90, 230, 210, 230, 120, 230, 210, 120],
            noon: [23, 75, 12, 97, 21, 67, 98, 21, 43, 64, 76, 38],
            night: [34, 87, 32, 76, 98, 12, 32, 87, 39, 36, 29, 36]
        }
        var line_option = {
            color: ['#00f2f1'],
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['实际销售额'],
                textStyle: {
                    color: '#4c9bfd' // 图例文字颜色
                },
                right: '10%' // 距离右边10%
            },
            grid: {
                top: "20%",
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true,
                show: true,// 显示边框
                borderColor: '#012f4a'// 边框颜色
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                axisTick: {
                    show: false // 去除刻度线
                },
                axisLabel: {
                    color: '#4c9bfd' // 文本颜色
                },
                axisLine: {
                    show: false // 去除轴线
                }
            },
            yAxis: {
                type: 'value',
                axisTick: {
                    show: false  // 去除刻度
                },
                axisLabel: {
                    color: '#4c9bfd' // 文字颜色
                },
                splitLine: {
                    lineStyle: {
                        color: '#012f4a' // 分割线颜色
                    }
                }
            },
            series: [
                {
                    smooth: true,
                    name:'实际销售额',
                    type: 'line',
                    stack: 'Total',
                    data:  line_data.morning
                }
            ]
        };

        lineChart.setOption(line_option);
        // 切换
        $('.sales').on('click', '.caption a', function(){
            // 样式
            $(this).addClass('active').siblings().removeClass('active')
            // currData 当前对应的数据
            // this.dataset.type 标签上的data-type属性值，对应data中的属性
            var currData = line_data[this.dataset.type]
            // 修改图表1的数据
            line_option.series[0].data = currData
            // 重新设置数据  让图标重新渲染
            lineChart.setOption(line_option)
        })

        window.addEventListener("resize", function () {
            lineChart.resize();
        })

        // 1. 实例化对象
        var radarChart = echarts.init(document.querySelector(".radar"));
        // 2.指定配置
        var radar_dataBJ = [[55, 9, 56, 0.46, 18, 6, 1]];
        var radar_option = {
            tooltip: {
                show: true,
                // 控制提示框组件的显示位置
                position: ['60%', '10%'],
            },
            radar: {
                center: ['50%', '50%'],
                indicator: [
                    { name: '北京市', max: 100 },
                    { name: '广东省', max: 100 },
                    { name: '香港', max: 100 },
                    { name: '浙江省', max: 100 },
                    { name: '河南省', max: 100 }
                ],
                radius: "65%",
                shape: "circle",
                splitNumber: 4,
                name: {
                    textStyle: {
                        color: "#4c9bfd"
                    }
                },
                splitLine: {
                    lineStyle: {
                        color: "rgba(255,255,255,0.5)"
                    }
                },
                splitArea: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: "rgba(250, 250, 250, 0.5)"
                    }
                }
            },
            series: [
                {
                    name: "北京",
                    type: "radar",
                    lineStyle: {
                        normal: {
                            color: '#fff',
                            // width: 1
                        }
                    },
                    data: [[90, 19, 56, 11, 34]],
                    symbol: "circle",
                    symbolSize: 5,
                    // 小圆点（拐点）设置为白色
                    itemStyle: {
                        color: '#fff'
                    },
                    // 在圆点上显示相关数据
                    label: {
                        show: true,
                        color: '#fff',
                        fontSize: 10
                    },
                    areaStyle: {
                        color: "rgba(238, 197, 102, 0.6)"
                    }
                }
            ]
        };
        // 3.把配置和数据给对象
        radarChart.setOption(radar_option);

        window.addEventListener("resize", function () {
            radarChart.resize();
        })

        // 1. 实例化对象
        var gaugeChart = echarts.init(document.querySelector(".gauge"));
        // 2. 指定数据和配置
        var gauge_option = {
            series: [
                {
                    name: "销售进度",
                    type: "pie",
                    // 放大图形
                    radius: ['130%', '150%'],
                    // 移动下位置  套住50%文字
                    center: ['48%', '80%'],
                    //是否启用防止标签重叠策略
                    // avoidLabelOverlap: false,
                    labelLine: {
                        normal: {
                            show: false
                        }
                    },
                    startAngle: 180,
                    hoverOffset: 0,
                    data: [
                        {
                            value: 100,
                            itemStyle: {
                                color: new echarts.graphic.LinearGradient(
                                    // (x1,y2) 点到点 (x2,y2) 之间进行渐变
                                    0,
                                    0,
                                    0,
                                    1,
                                    [
                                        { offset: 0, color: "#00c9e0" }, // 0 起始颜色
                                        { offset: 1, color: "#005fc1" } // 1 结束颜色
                                    ]
                                )
                            }
                        },
                        { value: 100 , itemStyle: { color: '#12274d' } },
                        { value: 200 , itemStyle: {color: "transparent"}}
                    ]
                }
            ]
        };
        // 3. 把数据和配置给实例对象
        gaugeChart.setOption(gauge_option);
        window.addEventListener("resize", function () {
            gaugeChart.resize();
        })

        /*热销模块*/
        // 1. 准备数据
        var hotData = [
            {
                city: '北京',  // 城市
                sales: '25, 179',  // 销售额
                flag: true, //  上升还是下降
                brands: [   //  品牌种类数据
                    { name: '可爱多', num: '9,086', flag: true },
                    { name: '娃哈哈', num: '8,341', flag: true },
                    { name: '喜之郎', num: '7,407', flag: false },
                    { name: '八喜', num: '6,080', flag: false },
                    { name: '小洋人', num: '6,724', flag: false },
                    { name: '好多鱼', num: '2,170', flag: true },
                ]
            },
            {
                city: '河北',
                sales: '23,252',
                flag: false,
                brands: [
                    { name: '可爱多', num: '3,457', flag: false },
                    { name: '娃哈哈', num: '2,124', flag: true },
                    { name: '喜之郎', num: '8,907', flag: false },
                    { name: '八喜', num: '6,080', flag: true },
                    { name: '小洋人', num: '1,724', flag: false },
                    { name: '好多鱼', num: '1,170', flag: false },
                ]
            },
            {
                city: '上海',
                sales: '20,760',
                flag: true,
                brands: [
                    { name: '可爱多', num: '2,345', flag: true },
                    { name: '娃哈哈', num: '7,109', flag: true },
                    { name: '喜之郎', num: '3,701', flag: false },
                    { name: '八喜', num: '6,080', flag: false },
                    { name: '小洋人', num: '2,724', flag: false },
                    { name: '好多鱼', num: '2,998', flag: true },
                ]
            },
            {
                city: '江苏',
                sales: '23,252',
                flag: false,
                brands: [
                    { name: '可爱多', num: '2,156', flag: false },
                    { name: '娃哈哈', num: '2,456', flag: true },
                    { name: '喜之郎', num: '9,737', flag: true },
                    { name: '八喜', num: '2,080', flag: true },
                    { name: '小洋人', num: '8,724', flag: true },
                    { name: '好多鱼', num: '1,770', flag: false },
                ]
            },
            {
                city: '山东',
                sales: '20,760',
                flag: true,
                brands: [
                    { name: '可爱多', num: '9,567', flag: true },
                    { name: '娃哈哈', num: '2,345', flag: false },
                    { name: '喜之郎', num: '9,037', flag: false },
                    { name: '八喜', num: '1,080', flag: true },
                    { name: '小洋人', num: '4,724', flag: false },
                    { name: '好多鱼', num: '9,999', flag: true },
                ]
            }
        ]
        var supHTML = "";
        $.each(hotData, function(index, item) {
            // console.log(item);
            supHTML += `<li><span>${item.city}</span><span> ￥${item.sales} </span></li>`;
        });
        $(".sup").html(supHTML);

        $(".province .sup").on("mouseenter", "li", function () {
            $(this).addClass("active").siblings().removeClass();
            var subHTML = "";
            $.each(hotData[$(this).index()].brands, function(index, item) {
                // 是对应城市的每一个品牌对象
                // console.log(item);
                subHTML += `<li><span>${item.name}</span><span> ￥${item.num}</span></li>`;
            });
            // 把生成的6个小li字符串给 sub dom盒子
            $(".sub").html(subHTML);
        })

        var $lis = $('.province .sup li')
        // 第一个默认激活
        $lis.eq(0).mouseenter()

        // 1. 实例化对象
        var geoChart = echarts.init(document.querySelector(".geo"));


        function randomData() {
            return Math.round(Math.random()*500);
        }
        var geo_data = [
            {name: '北京',value: '100' },{name: '天津',value: randomData() },
            {name: '上海',value: randomData() },{name: '重庆',value: randomData() },
            {name: '河北',value: randomData() },{name: '河南',value: randomData() },
            {name: '云南',value: randomData() },{name: '辽宁',value: randomData() },
            {name: '黑龙江',value: randomData() },{name: '湖南',value: randomData() },
            {name: '安徽',value: randomData() },{name: '山东',value: randomData() },
            {name: '新疆',value: randomData() },{name: '江苏',value: randomData() },
            {name: '浙江',value: randomData() },{name: '江西',value: randomData() },
            {name: '湖北',value: randomData() },{name: '广西',value: randomData() },
            {name: '甘肃',value: randomData() },{name: '山西',value: randomData() },
            {name: '内蒙古',value: randomData() },{name: '陕西',value: randomData() },
            {name: '吉林',value: randomData() },{name: '福建',value: randomData() },
            {name: '贵州',value: randomData() },{name: '广东',value: randomData() },
            {name: '青海',value: randomData() },{name: '西藏',value: randomData() },
            {name: '四川',value: randomData() },{name: '宁夏',value: randomData() },
            {name: '海南',value: randomData() },{name: '台湾',value: randomData() },
            {name: '香港',value: randomData() },{name: '澳门',value: randomData() }
        ];

        var geo_option = {
            backgroundColor: 'transparent',
            tooltip : {
                trigger: 'item',
                formatter: function(params) {
                    // console.log(params);
                    let data = params.data;
                    // let data2 = data1.data;
                    return "<b>异常详情<b><br>"+data.name+"："+data.value;
                }
            },



            //配置属性
            series: [{
                name: '数据',
                type: 'map',
                mapType: 'china',
                roam: true,
                zoom: 1.25,
                itemStyle: {
                    normal: {
                        areaColor: "#142957",
                        borderColor: "#195BB9",
                        borderWidth: 1
                    },
                    emphasis: {
                        areaColor: "#2B91B7"
                    }
                },
                label: {
                    normal: {
                        show: true, //省份名称
                        color: "#46bee9",
                        width: 1,
                        opacity: 0.6,
                    },
                    emphasis: {
                        show: false
                    }
                },
                data:geo_data  //数据
            }]
        };


        //使用制定的配置项和数据显示图表
        geoChart.setOption(geo_option);

    </script>
</body>
</html>
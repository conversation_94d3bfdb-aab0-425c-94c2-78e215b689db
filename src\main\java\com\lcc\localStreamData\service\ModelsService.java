package com.lcc.localStreamData.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lcc.localStreamData.entity.Models;

public interface ModelsService extends IService<Models> {


    Page<Models> findListAdmin(int page, int pageSize, String trainer);

    Page<Models> findListUser(int page, int pageSize, Integer trainerId);
}

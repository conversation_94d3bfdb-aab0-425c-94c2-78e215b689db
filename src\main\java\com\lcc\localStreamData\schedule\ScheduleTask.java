package com.lcc.localStreamData.schedule;

import com.lcc.localStreamData.entity.Rule;
import com.lcc.localStreamData.entity.RuleGroup;
import com.lcc.localStreamData.service.StreamDataService;
import com.lcc.localStreamData.service.TestDataProcessService;
import com.lcc.localStreamData.service.TestDataProcessServiceCopy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


@Component
@Slf4j
public class ScheduleTask {

    @Autowired
    private TestDataProcessService testDataProcessService;
    @Autowired
    private StreamDataService streamDataService;
    private static List<Rule> defaultRuleList;

    static {
        //默认规则组限制10分钟内消费5次以上和10分钟内异地消费
        Rule rule1 = new Rule();
        rule1.setType("1");
        rule1.setTimeLimit(600);
        rule1.setFreqLimit(5);
        Rule rule2 = new Rule();
        rule2.setType("2");
        rule2.setTimeLimit(600);

        defaultRuleList = Arrays.asList(rule1, rule2);
    }

    @Scheduled(fixedRate = 300000)
    public void Strategy1Task() {
        testDataProcessService.addDataByStrategy1();
    }

    @Scheduled(fixedRate = 300000)
    public void Strategy2Task() {
        testDataProcessService.addDataByStrategy2();
    }

    @Scheduled(fixedRate = 300000)
    public void Strategy3Task() {
        testDataProcessService.addDataByStrategy3();
    }

    /**
     * 每30分钟自动评估一批数据
     */
    @Scheduled(fixedRate = 1000 * 60 * 30)
    public void autoTest() {
        //自动评估30分钟前到现在的数据
        LocalDateTime end = LocalDateTime.now();
        LocalDateTime start = end.minusMinutes(30);

        streamDataService.autoTestByDefaultRuleList(start, end, defaultRuleList);
    }

    @Scheduled(cron = "0 59 23 * * ?")
    public void resetTask() {
        testDataProcessService.resetData();
    }

}

package com.lcc.localStreamData.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcc.localStreamData.common.R;
import com.lcc.localStreamData.entity.StreamData;
import com.lcc.localStreamData.service.StreamDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/stream")
public class StreamDataController {

    @Autowired
    private StreamDataService streamDataService;


    @GetMapping("/list")
    public R<Page<StreamData>> list(int page, int pageSize, String place) {
        Page<StreamData> list = streamDataService.findList(page, pageSize, place);
        return R.success(list);
    }

    @GetMapping("/userList")
    public R<Page<StreamData>> userList(int page, int pageSize) {
        Page<StreamData> list = streamDataService.findUserList(page, pageSize);
        return R.success(list);
    }

    @GetMapping("/delete")
    public R<String> deleteById(String id) {
        streamDataService.removeById(id);
        return R.success("操作成功");
    }

    @GetMapping("/mark")
    public R<String> mark(String id) {
        streamDataService.mark(id);
        return R.success("操作成功");
    }

    @PostMapping("/save")
    public R<String> save(@RequestBody StreamData streamData) {
//        testDataService.save(testData);
        return R.success("操作成功");
    }


}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/elementui.css">
    <script src="js/echarts.min.js"></script>
    <script src="js/jquery.min.js"></script>
    <script src="js/china.js"></script>
    <style>
        #app {
            padding: 20px;
        }
        .page {
            margin-top: 10px;
            text-align: center;
        }
        .header {

        }
        .header .search {
            width: 230px;
        }
        .table {
            border: 2px solid #f3f4f7;
            margin-top: 20px;

        }

        .left {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 3px 3px 2px #e7e7e7;
            padding-top: 20px;
            padding-bottom: 20px;
            height: 685px;
        }

        .right {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 3px 3px 2px #e7e7e7;
            padding-bottom: 20px;
            height: 685px;
            background-image:url(images/bg.jpg);
        }


        .bread {
            margin-bottom: 20px;
            background-color: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 3px 3px 2px #e7e7e7;
        }

        .right .title {
            font-size: 25px;
            font-weight: bold;
            border-bottom: 1px solid #303133;
            padding-top: 20px;
            padding-bottom: 20px;
            color: #303133;
            text-align: center;
            background-color: #fff;
        }

        .right .content {
            margin-top: 50px;
            height: 580px;
        }

        .switch {
            margin-top: 8px;
        }

        #chart1 {
            width: 100%;
            height: 600px;
        }
        #chart2 {
            width: 100%;
            height: 600px;
        }

    </style>
</head>
<body>
<div id="app">

    <div class="bread">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item><a href="/">首页</a></el-breadcrumb-item>
            <el-breadcrumb-item><a href="/">用户预警</a></el-breadcrumb-item>
        </el-breadcrumb>
    </div>

    <el-row :gutter="20">
        <el-col :span="8">
            <div class="left">
                <div class="table">
                    <el-table
                            :data="tableData"
                            stripe
                            style="width: 100%">
                        <el-table-column
                                type="index"
                                width="80">
                        </el-table-column>
                        <el-table-column
                                prop="streamUserId"
                                label="用户ID"
                                width="150">
                        </el-table-column>
                        <el-table-column
                                prop="streamSignLocation"
                                label="注册地"
                                width="150">
                        </el-table-column>
                        <el-table-column label="操作">
                            <template slot-scope="scope">
                                <el-button type="primary" icon="el-icon-search" plain @click="analyseUser(scope.row.streamUserId)">用户分析</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="page">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page.sync="page"
                            :page-size="pageSize"
                            layout="prev, pager, next, jumper"
                            :total="totalData">
                    </el-pagination>
                </div>
            </div>
        </el-col>
        <el-col :span="16">
            <div class="right">
                <div class="title">{{ userId }} 用户分析结果</div>
                <div class="content"
                     v-loading="loading"
                     element-loading-text="拼命加载中"
                     element-loading-spinner="el-icon-loading"
                     element-loading-background="rgba(0, 0, 0, 0.8)">
                    <el-row :gutter="24">
                        <el-col :span="13">
                            <div id="chart1"></div>
                        </el-col>
                        <el-col :span="11">
                            <div>

                                <div>
                                    <el-row :gutter="20">
                                        <el-col :span="12">
                                            <dv-border-box-1>
                                                <dv-active-ring-chart :config="pieConfig" style="width:300px;height:300px;margin-left: -35px" />
                                            </dv-border-box-1>
                                        </el-col>
                                        <el-col :span="12">
                                            <dv-border-box-1>
                                                <dv-charts :option="normalOption" style="width:300px;height:300px;margin-left: -32px"/>
                                            </dv-border-box-1>
                                        </el-col>
                                    </el-row>
                                </div>

                                <div style="height:260px;margin-top: 20px;">
                                    <dv-border-box-1>
                                        <div style="height: 25px"></div>
                                        <div style="margin-left: 50px;">
                                            <dv-capsule-chart :config="listConfig" style="width:86%;height:220px" />
                                        </div>
                                    </dv-border-box-1>
                                </div>

                            </div>

                        </el-col>
                    </el-row>
                </div>
            </div>
        </el-col>
    </el-row>





</div>
</body>
<script src="js/vue.js"></script>
<script src="js/axios.js"></script>
<script src="js/elementui.js"></script>
<script src="js/coord.js"></script>
<!--调试版-->
<script src="js/dataV.js"></script>
<script>
    new Vue({
        el: "#app",
        data() {
            return {
                tableData: [{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                }],
                userId: 60951,
                page: 1,
                pageSize: 9,
                totalData: 400,
                condition: "",
                opt: "",
                isFirst: true,
                loading: true,
                pieConfig: {
                    data: [
                        {
                            name: '周口',
                            value: 55
                        },
                        {
                            name: '南阳',
                            value: 120
                        },
                        {
                            name: '西峡',
                            value: 78
                        },
                        {
                            name: '驻马店',
                            value: 66
                        },
                        {
                            name: '新乡',
                            value: 80
                        }
                    ]
                },
                listConfig: {
                    data: [
                        {
                            name: '南阳',
                            value: 167
                        },
                        {
                            name: '周口',
                            value: 67
                        },
                        {
                            name: '漯河',
                            value: 123
                        },
                        {
                            name: '郑州',
                            value: 55
                        },
                        {
                            name: '西峡',
                            value: 98
                        }
                    ]
                },
                normalOption: {
                    title: {
                        text: '异常消费占比',
                        style: {
                            fill: '#fff'
                        }
                    },
                    series: [
                        {
                            type: 'gauge',
                            data: [ { name: 'itemA', value: 55 } ],
                            center: ['50%', '55%'],
                            axisLabel: {
                                formatter: '{value}%',
                                style: {
                                    fill: '#fff'
                                }
                            },
                            axisTick: {
                                style: {
                                    stroke: '#fff'
                                }
                            },
                            animationCurve: 'easeInOutBack'
                        }
                    ]
                },
                geoData: []
            }
        },
        methods: {
            handleSizeChange(val) {
                this.pageSize=val;
                this.page=1;
                this.getList();
            },
            handleCurrentChange(val) {
                this.page=val;
                this.getList();
            },
            goBack() {
                console.log('go back');
            },
            getList() {
                axios.get(`/stream/userList?page=${this.page}&pageSize=${this.pageSize}`)
                    .then(response => {
                        // console.log(response.data)
                        let data = response.data.data;
                        this.totalData = data.total;
                        this.tableData = data.records;

                        if (this.isFirst){
                            this.userId = this.tableData[0].streamUserId
                            this.isFirst = false;
                            this.analyseUser(this.userId)
                        }
                        // console.log(this.tableData);
                    })
            },
            analyseUser(userId) {
                //查询开始，开启loading效果
                this.loading = true;

                this.userId = userId
                // 请求 得到可视化结果
                axios.get(`/userPaint?id=${userId}`).then(response => {

                    let result = response.data.data

                    //geo可视化
                    for (let i = 0; i < result.geoList.length; i++) {
                        result.geoList[i].name = result.geoList[i].name.split(",")[1]
                    }

                    this.geoData = result.geoList
                    let chart1 = echarts.init(document.getElementById("chart1"));
                    function convertData(data) {
                        var res = [];

                        console.log(data)
                        console.log(geoCoordMap)

                        for (var i = 0; i < data.length; i++) {
                            var geoCoord = geoCoordMap[data[i].name];
                            if (geoCoord) {
                                res.push({
                                    name: data[i].name,
                                    value: geoCoord.concat(data[i].value)
                                });
                            }
                        }

                        console.log(res)

                        return res;
                    }
                    let geoOption = {
                        geo: {
                            show: true,
                            map: 'china',
                            label: {
                                emphasis: {
                                    show: true,
                                    color: '#fff'
                                }
                            },
                            animationDurationUpdate: 0,
                            roam: true,
                            itemStyle: {
                                normal: {
                                    areaColor: '#01215c',
                                    borderWidth: 1,//设置外层边框
                                    borderColor:'#9ffcff',
                                    shadowColor: 'rgba(0,54,255, 1)',
                                    shadowBlur: 30
                                },
                                emphasis:{
                                    areaColor: '#01215c',
                                }
                            }
                        },
                        tooltip: {
                            trigger: 'item',
                            formatter: function(params) {
                                return params.name + ' : ' + (params.value+"").split(",")[2];
                            }
                        },
                        series: [
                            {
                                type: 'map',
                                map: 'china',
                                geoIndex: 0,
                                aspectScale: 0.75, //长宽比
                                showLegendSymbol: false, // 存在legend时显示
                                tooltip:{
                                    show:false
                                },
                                label: {
                                    normal: {
                                        show: false
                                    },
                                    emphasis: {
                                        show: false
                                    }
                                } ,
                                roam: false,

                                itemStyle: {
                                    normal: {
                                        areaColor: '#01215c',
                                        borderColor: '#3074d0',
                                        borderWidth: 1
                                    },
                                    emphasis: {
                                        areaColor: '#01215c'
                                    }
                                },
                            },
                            {
                                name: '散点',
                                type: 'effectScatter',
                                coordinateSystem: 'geo',
                                data: convertData(this.geoData),
                                symbolSize: 15,//调整散点大小
                                symbol: 'circle',
                                label: {
                                    normal: {
                                        show: true, //城市名称
                                        color: "#fff",
                                        width: 1,
                                        opacity: 0.6,
                                        offset: [10, -20],
                                        formatter: '{b}'
                                    },
                                    emphasis: {
                                        show: false
                                    }
                                },
                                showEffectOn: 'render',
                                itemStyle: {
                                    normal: {
                                        color: {
                                            type: 'radial',
                                            x: 0.5,
                                            y: 0.5,
                                            r: 0.5,
                                            colorStops: [{
                                                offset: 0,
                                                color: 'rgba(14,245,209,0.2)'
                                            }, {
                                                offset: 0.8,
                                                color: 'rgba(14,245,209,0.2)'
                                            }, {
                                                offset: 1,
                                                color: 'rgba(14,245,209,1)'
                                            }],
                                            global: false // 缺省为 false
                                        },
                                    }

                                },

                            },
                        ]
                    }
                    chart1.setOption(geoOption);

                    //消费地占比可视化
                    this.pieConfig.data = result.geoList
                    this.pieConfig={...this.pieConfig}//解决dataV更新数据不生效


                    //消费类型可视化
                    let typeList = result.typeList
                    let type = [
                        [5812,5811,5813],
                        [7011,4722,5561],
                        [5941,7941,7997,7933,7992],
                        [5732],
                        [4816,5374,7372],
                        [7911,7932,5813],
                        [5631,5977,7298],
                        [5611],
                        [5013,5021,5039,5044,5045,5046,5047,5051,5065,5074,5072,5193,5111,5122,5131,5137,5139,5172,5192,5198,5998,5398,4458],
                        [5541,5542]
                    ]
                    let type_arr = ["美食","旅游","体育","电子","IT","年轻活力","女性","男性","商人","开车一族"]
                    let values = [];
                    let places=[];
                    for (let i = 0; i < typeList.length; i++) {
                        let index=-1;
                        for (let j = 0; j < 10; j++) {
                            if (type[j].includes(typeList[i].streamConsumeType)){
                                index=j;
                            }
                        }
                        let num=places.indexOf(type_arr[index])
                        if (num==-1){
                            let item = { name: type_arr[index], value: typeList[i].nums }
                            values.push(item);
                            places.push(type_arr[index])
                        }else{
                            values[num].value+=typeList[i].nums;
                        }
                    }
                    this.listConfig.data = values
                    this.listConfig={...this.listConfig}//解决dataV更新数据不生效

                    this.normalOption.series[0].data = [{ name: '异常消费', value: result.normalPercent }]
                    this.normalOption={...this.normalOption}//解决dataV更新数据不生效



                    //查询结束，关闭loading效果
                    this.loading = false;
                })
            }
        },
        created() {
            this.getList()
        }
    });
</script>
<script>



    var chart2 = echarts.init(document.getElementById("chart2"));
    var chart2_option = {
        title: {
            text: '用户消费分布',
            left: 'center'
        },
        tooltip: {
            trigger: 'item'
        },
        series: [
            {
                name: 'Access From',
                type: 'pie',
                radius: '50%',
                data: [
                    { value: 1048, name: 'Search Engine' },
                    { value: 735, name: 'Direct' },
                    { value: 580, name: 'Email' },
                    { value: 484, name: 'Union Ads' },
                    { value: 300, name: 'Video Ads' }
                ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    };
    chart2.setOption(chart2_option);


    window.addEventListener("resize", function () {
        chart1.resize();
        chart2.resize();
    })
</script>
</html>
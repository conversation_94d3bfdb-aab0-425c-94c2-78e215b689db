package com.lcc.localStreamData.service.impl;

import com.lcc.localStreamData.common.R;
import com.lcc.localStreamData.entity.User;
import com.lcc.localStreamData.service.RegisterService;
import com.lcc.localStreamData.service.UserService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class RegisterServiceImpl implements RegisterService {
    @Autowired
    UserService userService;

    @Override
    public R register(User user) {
        if (StringUtils.isEmpty(user.getUsername())) {
            return R.error("用户名不能为空");
        }
        if (StringUtils.isEmpty(user.getPassword())) {
            return R.error("密码不能为空");
        }
        if (!userService.isUsernameUnique(user.getUsername())) {
            return R.error("用户名已存在");
        }
        user.setUserLevel("0");
        user.setIsActive("1");
        user.setCreateTime(LocalDateTime.now());
        userService.save(user);
        return R.success("注册成功");
    }
}

package com.lcc.localStreamData.controller;

import com.lcc.localStreamData.common.R;
import com.lcc.localStreamData.entity.StreamData;
import com.lcc.localStreamData.service.TestDataProcessServiceCopy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 大屏展示模块
 */
@Slf4j
@RestController
@RequestMapping("/show")
public class ShowController {

    @Autowired
    private TestDataProcessServiceCopy testDataProcessService;

    @GetMapping("/overview")
    public R<Map> overview(){

        Map overview = testDataProcessService.overview();
        return R.success(overview);
    }

    @GetMapping("/type")
    public R<List> type(){

        List<Map<String, Object>> list = testDataProcessService.type();
        return R.success(list);
    }

    @GetMapping("/sign")
    public R<List> sign(){

        List<Map<String, Object>> list = testDataProcessService.sign();
        return R.success(list);
    }


    @GetMapping("/consumeTop13")
    public R<List> consumeTop13(){
        List<Map<String, Object>> list = testDataProcessService.consumeTop13(getNowDate());
        return R.success(list);
    }

    @GetMapping("/nationTop")
    public R<List> nationTop(){
        List<Map> list = testDataProcessService.nationTop(getNowDate());
        return R.success(list);
    }

    @GetMapping("/line/{time}")
    public R<long[]> nationTop(@PathVariable(value = "time") String time){
        long[] line = testDataProcessService.line(getNowDate(), time);
        return R.success(line);
    }

    @GetMapping("/hot/province")
    public R<List> hotProvince(){
        List<Map> list = testDataProcessService.hotProvince(getNowDate());
        return R.success(list);
    }

    @GetMapping("/hot/sale/{place}")
    public R<List> hotSale(@PathVariable(value = "place") String place){
        List<Map> hotSale = testDataProcessService.hotSale(getNowDate(), place);
        return R.success(hotSale);
    }

    @GetMapping("/normal")
    public R<List> normal(){
        List<StreamData> streamData = testDataProcessService.normalData();
        return R.success(streamData);
    }

    @GetMapping("/normalPercent")
    public R<Map> normalPercent(){
        Map map = testDataProcessService.normalPercent();
        return R.success(map);
    }

    @GetMapping("/normalByTime")
    public R<Map> normalByTime(String time){
        Map map = testDataProcessService.normalByTime(time);
        return R.success(map);
    }

    private String getNowDate(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date();
        String formattedDate = sdf.format(date);
        return formattedDate;
    }

}

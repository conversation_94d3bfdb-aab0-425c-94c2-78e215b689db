<template>
  <div class="login-container">
    <el-row :gutter="20">
      <el-col :span="5">
        <div class="logo">
          <img src="/images/logo1.jpg" alt="" />
        </div>
      </el-col>
      <el-col :span="15">
        <div style="height: 70px"></div>
        <span class="title">面向金融行业流数据的在线分析系统</span>
      </el-col>
    </el-row>

    <div class="login-form">
      <div class="content">
        <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef" label-width="100px" class="demo-ruleForm">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="ruleForm.username" placeholder="请输入用户名"></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input type="password" v-model="ruleForm.password" placeholder="请输入密码"
              @keyup.enter="submitForm"></el-input>
          </el-form-item>
          <el-form-item label="验证码" prop="code">
            <el-input v-model="ruleForm.code" placeholder="请输入验证码" @keyup.enter="submitForm"></el-input>
          </el-form-item>
          <el-form-item>
            <img style="width:160px;height:60px;cursor:pointer;" :src="imgSrc" @click="getCaptcha()" />
          </el-form-item>
          <el-form-item style="margin-left: -40px;">
            <el-button type="primary" @click="submitForm" :loading="loading">
              立即登录
            </el-button>
            <el-button @click="resetForm">重置</el-button>
            <el-button type="warning" @click="register">注册</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import axios from 'axios'

const router = useRouter()
const ruleFormRef = ref()
const loading = ref(false)
const imgSrc = ref('')

const ruleForm = reactive({
  username: '',
  password: '',
  code: '',
  uuid: ''
})

const rules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ]
})

// 生成随机字符串作为 UUID
const generateRandomString = () => {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const length = 32
  const values = new Uint8Array(length)
  window.crypto.getRandomValues(values)
  let str = ''
  for (let i = 0; i < length; i++) {
    str += charset[values[i] % charset.length]
  }
  return str
}

const getCaptcha = async () => {
  try {
    // 获取或生成 UUID
    ruleForm.uuid = sessionStorage.getItem('uuid')
    if (!ruleForm.uuid) {
      ruleForm.uuid = generateRandomString()
      sessionStorage.setItem('uuid', ruleForm.uuid)
    }

    // 使用正确的验证码接口
    imgSrc.value = `/captcha/generate?uuid=${ruleForm.uuid}&v=${Date.now()}`
  } catch (error) {
    console.error('获取验证码失败:', error)
  }
}

const submitForm = async () => {
  if (!ruleFormRef.value) return

  await ruleFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const response = await axios.post('/login', ruleForm)
        if (response.data.code === 1) {
          localStorage.setItem('userInfo', JSON.stringify(response.data.data))
          ElMessage.success('登录成功')
          router.push('/')
        } else {
          ElMessage.error(response.data.msg || '登录失败')
          getCaptcha() // 刷新验证码
        }
      } catch (error) {
        console.error('登录错误:', error)
        ElMessage.error('登录失败，请检查网络连接')
        getCaptcha() // 刷新验证码
      } finally {
        loading.value = false
      }
    }
  })
}

const resetForm = () => {
  if (!ruleFormRef.value) return
  ruleFormRef.value.resetFields()
}

const register = () => {
  router.push('/register')
}

onMounted(() => {
  getCaptcha()
})
</script>

<style scoped>
.login-container {
  background: url('/images/login-bg.jpg') no-repeat center center;
  background-size: cover;
  height: 100vh;
  padding: 50px;
}

.logo img {
  width: 100%;
  max-width: 300px;
}

.title {
  font-size: 36px;
  font-weight: bold;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.login-form {
  position: absolute;
  top: 50%;
  right: 10%;
  transform: translateY(-50%);
  width: 400px;
  background: rgba(255, 255, 255, 0.9);
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

.content {
  width: 100%;
}

.demo-ruleForm {
  width: 100%;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-button {
  margin-right: 10px;
}
</style>

package com.lcc.localStreamData.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lcc.localStreamData.entity.NewData;
import com.lcc.localStreamData.mapper.NewDataMapper;
import com.lcc.localStreamData.service.NewDataService;
import org.springframework.stereotype.Service;


@Service
public class NewDataServiceImpl extends ServiceImpl<NewDataMapper, NewData> implements NewDataService {



    @Override
    public void addDataByStrategy1() {

    }

    @Override
    public void addDataByStrategy2() {

    }

    @Override
    public void addDataByStrategy3() {

    }
}

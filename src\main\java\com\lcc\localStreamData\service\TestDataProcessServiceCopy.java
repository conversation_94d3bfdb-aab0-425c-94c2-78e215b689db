package com.lcc.localStreamData.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lcc.localStreamData.entity.StreamData;

import java.util.List;
import java.util.Map;

public interface TestDataProcessServiceCopy extends IService<StreamData> {

    public Map overview();

    public List<Map<String, Object>> type();

    public List<Map<String, Object>> sign();

    public List<Map<String, Object>> consumeTop13(String date);

    public List<Map> nationTop(String date);

    public long[] line(String date,String time);

    public List<Map> hotProvince(String date);

    public List<Map> hotSale(String date,String place);

    public List<StreamData> normalData();

    public Map normalPercent();

    public Map normalByTime(String time);


}

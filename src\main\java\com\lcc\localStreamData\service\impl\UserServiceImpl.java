package com.lcc.localStreamData.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lcc.localStreamData.entity.User;
import com.lcc.localStreamData.mapper.UserMapper;
import com.lcc.localStreamData.service.UserService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;


@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private UserMapper userMapper;


    @Override
    public Page<User> findList(int page, int pageSize, String username) {
        Page pageInfo = new Page(page, pageSize);
        //构造条件构造器
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        //添加过滤条件
        if (StringUtils.isNotEmpty(username)) {
            queryWrapper.like("username", username);
        }
        //添加排序条件
        queryWrapper.orderByDesc("create_time");

        //执行查询
        Page result = this.page(pageInfo, queryWrapper);

        return result;
    }

    @Override
    public void active(int id) {
        UpdateWrapper<User> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        updateWrapper.set("is_active", 1);
        userMapper.update(null, updateWrapper);
    }

    @Override
    public void lock(int id) {
        UpdateWrapper<User> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        updateWrapper.set("is_active", 0);
        userMapper.update(null, updateWrapper);
    }

    @Override
    public boolean isUsernameUnique(String username) {
        if (StringUtils.isEmpty(username)) return false;//用户名为空返回false
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("username", username);
        if (super.getOne(wrapper) != null) {
            return false;
        }
        return true;
    }


}

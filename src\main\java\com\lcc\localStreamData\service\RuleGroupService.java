package com.lcc.localStreamData.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lcc.localStreamData.common.R;
import com.lcc.localStreamData.entity.Rule;
import com.lcc.localStreamData.entity.RuleGroup;
import com.lcc.localStreamData.entity.vo.RuleGroupVo;

import java.util.List;

public interface RuleGroupService extends IService<RuleGroup> {

    public Page<RuleGroupVo> findPage(int page, int pageSize, String name);

    public void saveRuleGroup(String name, List<Rule> ruleList);

    public void removeRuleGroup(Integer id);

    RuleGroupVo getRuleGroup(Integer id);
}

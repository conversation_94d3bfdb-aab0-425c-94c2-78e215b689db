<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/elementui.css">
    <style>
        body {
            background-color: #fff;
        }

        #app {
            padding: 20px;
        }

        .page {
            margin-top: 30px;
            text-align: center;
        }

        .header {

        }

        .header .search {
            width: 230px;
        }

        .table {
            border: 2px solid #f3f4f7;
            margin-top: 20px;
        }

        .bread {
            margin-bottom: 20px;
        }

        .addButton {
            text-align: right;
        }

        .model-option {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    </style>
</head>
<body>
<div id="app">

    <div class="bread">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item><a href="/">首页</a></el-breadcrumb-item>
            <el-breadcrumb-item><a href="/">模型训练</a></el-breadcrumb-item>
        </el-breadcrumb>
    </div>

    <div class="header">
        <el-row :gutter="20">
            <el-col :span="8">
                <div class="search">
                    <el-input
                            placeholder="请输入训练人"
                            v-model="trainer"
                            clearable
                            @keyup.enter.native="handleSearch"

                    >
                        <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
                    </el-input>
                </div>
            </el-col>
            <el-col :span="4" :offset="12">
                <div class="addButton">
                    <el-button type="primary" plain icon="el-icon-plus" @click="addModel">训练模型</el-button>
                </div>
            </el-col>
        </el-row>


    </div>
    <div class="table">
        <el-table
                :data="modelData"
                stripe
                style="width: 100%">
            <el-table-column
                    type="index"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="time"
                    label="发布日期"
                    width="180">
            </el-table-column>
            <el-table-column
                    prop="modelName"
                    label="模型名称"
                    width="180">
            </el-table-column>
            <el-table-column
                    prop="modelDescription"
                    label="模型介绍"
                    width="350">
            </el-table-column>
            <el-table-column
                    prop="trainingParameters"
                    label="训练参数"
                    width="180"
            >
            </el-table-column>
            <el-table-column
                    prop="trainer"
                    label="训练人">
            </el-table-column>
            <el-table-column
                    label="操作">
                <template slot-scope="scope">
                    <el-button type="text" @click="downloadParams(scope.row)">下载参数</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>

    <div class="page">
        <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="page"
                :page-sizes="[9, 20, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalData">
        </el-pagination>
    </div>

    <el-dialog title="新增训练模型" :visible.sync="dialogFormVisible" width="700px">
        <el-form :model="modelInfo">
            <el-form-item label="模型名称" :label-width="formLabelWidth">
                <el-input v-model="modelInfo.modelName" autocomplete="off" placeholder="请填写模型名称"></el-input>
            </el-form-item>
            <el-form-item label="类型" :label-width="formLabelWidth">
                <el-select v-model="modelInfo.id" placeholder="请选择模型类型">
                    <el-option label="XGBoost" value="1" class="model-option">
                        <span>XGBoost</span>
                        <el-tooltip effect="dark" content="高性能的梯度提升框架，用于构建预测模型。使用梯度提升技术来优化预测准确性。" placement="right">
                            <i class="el-icon-question"></i>
                        </el-tooltip>
                    </el-option>
                    <el-option label="GBDT" value="2" class="model-option">
                        <span>GBDT</span>
                        <el-tooltip effect="dark" content="梯度提升决策树方法，通过迭代地添加决策树来减少预测误差。" placement="right">
                            <i class="el-icon-question"></i>
                        </el-tooltip>
                    </el-option>
                    <el-option label="LogisticRegression" value="3" class="model-option">
                        <span>LogisticRegression</span>
                        <el-tooltip effect="dark" content="简单而强大的分类算法。使用对数几率函数对事件发生的概率进行建模。" placement="right">
                            <i class="el-icon-question"></i>
                        </el-tooltip>
                    </el-option>
                    <el-option label="SVM" value="4" class="model-option">
                        <span>SVM</span>
                        <el-tooltip effect="dark" content="寻找最大边界超平面以分隔不同类别的数据点。能够通过核技巧处理非线性可分的数据。" placement="right">
                            <i class="el-icon-question"></i>
                        </el-tooltip>
                    </el-option>
                    <el-option label="RandomForest" value="5" class="model-option">
                        <span>RandomForest</span>
                        <el-tooltip effect="dark" content="由多个决策树组成的集合模型。通过随机选取样本和特征训练每棵树来减少过拟合风险。"
                                    placement="right">
                            <i class="el-icon-question"></i>
                        </el-tooltip>
                    </el-option>
                    <el-option label="CART" value="6" class="model-option">
                        <span>CART</span>
                        <el-tooltip effect="dark" content="一种决策树算法，通过递归地分割数据集来创建树结构。使用基尼不纯度或方差作为节点分裂的标准。"
                                    placement="right">
                            <i class="el-icon-question"></i>
                        </el-tooltip>
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="训练数据集" :label-width="formLabelWidth">
                <el-upload
                        class="upload-demo"
                        drag
                        action="http://************:10408/upload"
                        :on-success="handleDataSuccess"
                >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                </el-upload>
                <div>
                    <span>(</span>
                    <el-button type="text" @click="downloadDemoDataset()">点此下载</el-button>
                    <span>查看示例数据集)</span>
                </div>
            </el-form-item>
            <el-form-item label="描述/备注" :label-width="formLabelWidth">
                <el-input type="textarea" v-model="modelInfo.modelDescription" placeholder="请填写模型描述\备注"></el-input>
            </el-form-item>


        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitModel">提交至本地</el-button>
            <el-button type="primary" @click="dialogFormVisible = false">提交至大数据平台</el-button>
            <el-button @click="dialogFormVisible = false;modelInfo = {}">取 消</el-button>
        </div>
    </el-dialog>
    <div
            v-loading.fullscreen.lock="fullscreenLoading"
            element-loading-text="模型训练中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
    >
    </div>
</div>
</body>
<script src="js/vue.js"></script>
<script src="js/axios.js"></script>
<script src="js/elementui.js"></script>
<script src="js/moment.js"></script>
<script src="js/jquery.min.js"></script>
<script>
    const remoteIp="************";
    new Vue({
        el: "#app",
        data() {
            return {
                fullscreenLoading: false,
                modelData: [],
                page: 1,
                pageSize: 9,
                totalData: 400,
                trainer: "",
                dialogFormVisible: false,
                formLabelWidth: '90px',
                modelInfo: {
                    modelName: "",
                    id: "",
                    modelDescription: "",
                    dataUrl: "",
                    trainer: -1
                },
                userInfo: {}
            }
        },
        methods: {
            handleDataSuccess(res, file) {
                this.modelInfo.dataUrl = file.response;
            },
            handleSizeChange(val) {
                this.pageSize = val;
                this.page = 1;
                this.refreshData();
            },
            handleCurrentChange(val) {
                this.page = val;
                this.refreshData();
            },
            goBack() {
                console.log('go back');
            },
            refreshData() {
                axios.get(`/models/list/admin?page=${this.page}&pageSize=${this.pageSize}&trainer=${this.trainer}`)
                    .then(response => {
                        // console.log(response.data)
                        let data = response.data.data;
                        //格式转换
                        for (let i = 0; i < data.records.length; i++) {
                            data.records[i].time = moment(data.records[i].time).format('YYYY-MM-DD HH:mm:ss');
                        }
                        this.totalData = data.total;
                        this.modelData = data.records;

                        console.log(this.modelData);
                    })
            },
            handleSearch() {
                this.page = 1;
                this.refreshData();
            },
            downloadParams(row) {
                window.open(`http://`+remoteIp+`:10408/downloadModelParams?id=${row.id}`);
            },
            addModel() {
                this.dialogFormVisible = true;
            },
            submitModel() {
                let that = this;
                that.modelInfo.trainer = that.userInfo.id;
                that.fullscreenLoading = true;
                $.ajax({
                    type: 'post',
                    url: `http://`+remoteIp+`:10408/model/train`,
                    data: that.modelInfo,
                    success: function (res) {
                        that.dialogFormVisible = false;
                        that.$message({
                            message: '模型训练成功',
                            type: 'success'
                        });
                        that.modelInfo = {}
                        that.fullscreenLoading = false;
                        that.refreshData();
                    }
                })
            },
            downloadDemoDataset() {
                window.open("http://"+remoteIp+":10408/download?filename=" + "demo.csv");
            },
        },
        created() {
            const userInfo = window.localStorage.getItem('userInfo')
            if (userInfo) {
                this.userInfo = JSON.parse(userInfo)
            }
            this.refreshData();
        }

    });
</script>
</html>
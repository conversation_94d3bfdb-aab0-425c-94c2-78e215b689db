<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/elementui.css">
    <style>
        body {
            background-color: #fff;
        }

        #app {
            padding: 20px;
        }

        .page {
            margin-top: 30px;
            text-align: center;
        }

        .header {

        }

        .header .search {

        }

        .table {
            border: 2px solid #f3f4f7;
            margin-top: 20px;
        }

        .bread {
            margin-bottom: 20px;
        }

        .switch {
            margin-top: 8px;
        }
    </style>
</head>
<body>
<div id="app">

    <div class="bread">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item><a href="/">首页</a></el-breadcrumb-item>
            <el-breadcrumb-item><a href="/">数据预处理</a></el-breadcrumb-item>
        </el-breadcrumb>
    </div>

    <div class="header">
        <el-row>
            <!--            <el-col :span="4">-->
            <!--                <el-input-->
            <!--                        placeholder="请输入数据集名称"-->
            <!--                        v-model="datasetName"-->
            <!--                        clearable-->
            <!--                        @keyup.enter.native="handleSearch"></el-input>-->
            <!--            </el-col>-->
            <!--            <el-col :span="3">-->
            <!--                <el-button icon="el-icon-search" @click="handleSearch"></el-button>-->
            <!--            </el-col>-->
            <!--            <el-col :span="2">-->
            <!--                <el-upload-->
            <!--                        class="upload-demo"-->
            <!--                        action="http://"+remoteIp+":10408/upload"-->
            <!--                        :multiple="false"-->
            <!--                        :show-file-list="false"-->
            <!--                        :before-upload="handleFileInfo"-->
            <!--                        :on-success="handleDatasetUploadSuccess">-->
            <!--&lt;!&ndash;                    <el-button><i class="el-icon-upload"></i>&ndash;&gt;-->
            <!--&lt;!&ndash;                        上传数据集&ndash;&gt;-->
            <!--&lt;!&ndash;                    </el-button>&ndash;&gt;-->
            <!--                </el-upload>-->
            <!--            </el-col>-->
        </el-row>


    </div>
    <div class="table">
        <el-table
                :data="tableData"
                stripe
                style="width: 100%">
            <el-table-column
                    type="index"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="datasetName"
                    label="数据集名称"
                    width="280"
                    :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column
                    prop="filename"
                    label="文件名"
                    width="300"
                    :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column
                    prop="size"
                    label="大小"
                    width="80">
                <template slot-scope="scope">
                    <span v-if="scope.row.size>1024*1024">{{parseFloat(scope.row.size / 1024 / 1024).toFixed(2)}}&nbsp;MB</span>
                    <span v-else-if="scope.row.size>1024">{{parseFloat(scope.row.size / 1024).toFixed(2)}}&nbsp;KB</span>
                    <span v-else-if="scope.row.size<=1024">{{parseFloat(scope.row.size).toFixed(2)}}&nbsp;B</span>
                </template>
            </el-table-column>
            <el-table-column
                    prop="status"
                    label="状态"
                    width="80">
                <template slot-scope="scope">
                    <span v-if="scope.row.status=='0'" style="color: #aa2e32">未处理</span>
                    <span v-else style="color: #00a65a;font-weight: bold">已处理</span>
                </template>
            </el-table-column>
            <el-table-column
                    prop="createTime"
                    label="创建时间"
                    width="150"
                    :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column
                    prop="permission"
                    label="权限"
                    width="80"
            >
            </el-table-column>
            <el-table-column label="操作">
                <template slot-scope="scope">
                    <el-button type="primary" size="medium" @click="showDialog(scope.row)">预处理</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>

    <el-dialog title="选择处理流程" :visible.sync="dialogFormVisible" width="700px">
        <el-form :model="processInfo">
            <el-form-item label="数据补齐方式" label-width="150px">
                <el-radio v-model="processInfo.dataFill" label="false" border>不补齐</el-radio>
                <el-radio v-model="processInfo.dataFill" label="default" border>默认方法</el-radio>
                <!--                <el-input v-model="modelInfo.dataFill" autocomplete="off" placeholder="请填写模型名称"></el-input>-->
            </el-form-item>
            <el-form-item label="归一化和标准化" label-width="150px">
                <el-radio v-model="processInfo.dataNormalize" label="false" border>不归一化和标准化</el-radio>
                <el-radio v-model="processInfo.dataNormalize" label="StandardScaler" border>标准化</el-radio>
                <el-radio v-model="processInfo.dataNormalize" label="MinMaxScaler" border>最大最小归一化</el-radio>
                <!--                <el-input v-model="modelInfo.dataFill" autocomplete="off" placeholder="请填写模型名称"></el-input>-->
            </el-form-item>
            <el-form-item label="不平衡数据处理" label-width="150px">
                <el-radio v-model="processInfo.balanceProcess" label="false" border>不处理</el-radio>
                <el-radio v-model="processInfo.balanceProcess" label="default" border>默认方法处理</el-radio>
                <!--                <el-input v-model="modelInfo.dataFill" autocomplete="off" placeholder="请填写模型名称"></el-input>-->
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <!--            <el-button type="primary" @click="submitModel">提交至本地</el-button>-->
            <el-button type="primary" @click="doPreprocess(processInfo)">处理</el-button>
            <el-button @click="dialogFormVisible = false;processInfo = {}">取 消</el-button>
        </div>
    </el-dialog>

    <el-dialog :title="previewData.title" :visible.sync="previewDialogVisible" width="700px">
        <el-table
                :data="previewData.values"
                stripe
                style="width: 100%">
            <el-table-column
                    v-for="item in previewData.columns"
                    :prop="item"
                    :label="item"
                    width="150"
                    :show-overflow-tooltip="true">
            </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
            <!--            <el-button type="primary" @click="submitModel">提交至本地</el-button>-->
            <el-button @click="previewDialogVisible = false;">取 消</el-button>
        </div>
    </el-dialog>


    <div class="page">
        <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="page"
                :page-sizes="[9, 20, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalData">
        </el-pagination>
    </div>

</div>
</body>
<script src="js/vue.js"></script>
<script src="js/axios.js"></script>
<script src="js/elementui.js"></script>
<script src="js/moment.js"></script>
<script src="js/jquery.min.js"></script>
<script>
    const remoteIp = "************";
    new Vue({
        el: "#app",
        data() {
            return {
                tableData: [],
                page: 1,
                pageSize: 9,
                totalData: 400,
                condition: "",
                datasetName: "",
                opt: "",
                dialogFormVisible: false,
                previewDialogVisible: false,
                datasetInfo: {
                    datasetName: "",
                    filename: "",
                    size: 0
                },
                processInfo: {
                    id: null,
                    filename: "",
                    dataFill: 'false',
                    dataNormalize: 'false',
                    balanceProcess: 'false',
                },
                previewData: { //预览表格
                    title: "",//表格标题
                    columns: [], //标题
                    values: [] //值
                }
            }
        },
        methods: {
            handleSizeChange(val) {
                this.pageSize = val;
                this.page = 1;
                this.getList();
            },
            handleCurrentChange(val) {
                this.page = val;
                this.getList();
            },
            handleSearch() {
                this.page = 1;
                this.getList();
            },
            handleFileInfo(file) {//保存原文件名和大小
                console.log(file)
                let datasetName = file.name.substr(0, file.name.lastIndexOf("."))//去掉后缀名
                this.datasetInfo.datasetName = datasetName
                this.datasetInfo.size = file.size
            },
            showDialog(row) {
                this.processInfo.id = row.id;
                this.processInfo.filename = row.filename;
                this.processInfo.dataFill = 'false';
                this.processInfo.dataNormalize = 'false';
                this.processInfo.balanceProcess = 'false';
                this.dialogFormVisible = true;
            },
            goBack() {
                console.log('go back');
            },
            downloadFile(filename) {
                window.open("http://" + remoteIp + ":10408/download?filename=" + filename);
            },
            doPreprocess(preprocessInfo) {
                console.log(preprocessInfo)
                //对接python端
                axios({
                    url: "http://" + remoteIp + ":10408/preprocess",
                    params: preprocessInfo,
                    method: "get"
                }).then(response => {
                    if (response.data.code == 1) {
                        this.dialogFormVisible = false
                        this.previewData = response.data.data
                        this.previewDialogVisible = true
                        this.getList();
                    } else {
                        this.$message.error(response.data.msg);
                    }
                })
            },
            preview(row) {
                let that = this
                axios.get("http://" + remoteIp + ":10408/preview?filename=" + row.filename).then(response => {
                    if (response.data.code == 1) {
                        console.log(response.data.data)
                        that.previewDialogVisible = true
                        that.previewData.title = row.status === "0" ? "数据集预览" : "预处理结果预览"
                        that.previewData.columns = response.data.data.columns
                        that.previewData.values = response.data.data.values
                    } else {
                        this.$message.error(response.data.msg);
                    }
                })
            },
            getList() {
                axios.get(`/dataset/list?page=${this.page}&pageSize=${this.pageSize}&datasetName=${this.datasetName}`)
                    .then(response => {
                        // console.log(response.data)
                        let data = response.data.data;
                        //格式转换
                        for (let i = 0; i < data.records.length; i++) {
                            data.records[i].createTime = moment(data.records[i].createTime).format('YYYY-MM-DD HH:mm:ss');
                        }
                        this.totalData = data.total;
                        this.tableData = data.records;

                        console.log(this.tableData);
                    })
            }
            ,
        },
        created() {
            this.getList();
        }
        ,
    })
    ;
</script>
</html>
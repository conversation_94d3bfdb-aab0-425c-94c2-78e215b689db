import pandas as pd
from sklearn.preprocessing import StandardScaler
from pre_processor.IPreprocessor import DataFramePreprocessor


class StandardScalerProcessor(DataFramePreprocessor):
    def __init__(self,columns=None):
        super().__init__()
        self.process_columns=columns
        self.scaler=StandardScaler()
    def do_process(self, df): # 处理逻辑
        process_columns=self.process_columns
        if process_columns is None: # 如果没有指定列，则使用所有列
            process_columns=df.columns
        df[process_columns]=self.scaler.fit_transform(df[process_columns])
        return df
    def use_in_predict(self)->bool: # 是否在预测时使用
        return True
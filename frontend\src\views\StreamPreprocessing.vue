<template>
  <div class="stream-preprocessing">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>流数据预处理</span>
          <el-button type="primary" @click="startPreprocessing" :loading="isProcessing">
            {{ isProcessing ? '处理中...' : '开始预处理' }}
          </el-button>
        </div>
      </template>

      <!-- 预处理配置 -->
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card shadow="never">
            <template #header>
              <h4>数据清洗配置</h4>
            </template>
            <el-form :model="preprocessConfig" label-width="120px">
              <el-form-item label="缺失值处理">
                <el-select v-model="preprocessConfig.missingValueStrategy">
                  <el-option label="删除记录" value="drop" />
                  <el-option label="均值填充" value="mean" />
                  <el-option label="中位数填充" value="median" />
                  <el-option label="前值填充" value="forward" />
                  <el-option label="零值填充" value="zero" />
                </el-select>
              </el-form-item>
              <el-form-item label="异常值检测">
                <el-checkbox-group v-model="preprocessConfig.outlierDetection">
                  <el-checkbox label="iqr">IQR方法</el-checkbox>
                  <el-checkbox label="zscore">Z-Score方法</el-checkbox>
                  <el-checkbox label="isolation">孤立森林</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="重复数据">
                <el-radio-group v-model="preprocessConfig.duplicateHandling">
                  <el-radio label="keep_first">保留第一个</el-radio>
                  <el-radio label="keep_last">保留最后一个</el-radio>
                  <el-radio label="remove_all">全部删除</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card shadow="never">
            <template #header>
              <h4>特征工程</h4>
            </template>
            <el-form :model="preprocessConfig" label-width="120px">
              <el-form-item label="数值特征">
                <el-checkbox-group v-model="preprocessConfig.numericalFeatures">
                  <el-checkbox label="standardization">标准化</el-checkbox>
                  <el-checkbox label="normalization">归一化</el-checkbox>
                  <el-checkbox label="log_transform">对数变换</el-checkbox>
                  <el-checkbox label="binning">分箱处理</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="类别特征">
                <el-checkbox-group v-model="preprocessConfig.categoricalFeatures">
                  <el-checkbox label="one_hot">独热编码</el-checkbox>
                  <el-checkbox label="label_encoding">标签编码</el-checkbox>
                  <el-checkbox label="target_encoding">目标编码</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="时间特征">
                <el-checkbox-group v-model="preprocessConfig.timeFeatures">
                  <el-checkbox label="hour">小时</el-checkbox>
                  <el-checkbox label="day_of_week">星期</el-checkbox>
                  <el-checkbox label="month">月份</el-checkbox>
                  <el-checkbox label="is_weekend">是否周末</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card shadow="never">
            <template #header>
              <h4>数据质量监控</h4>
            </template>
            <div class="quality-metrics">
              <div class="metric-item">
                <div class="metric-label">完整性</div>
                <el-progress 
                  :percentage="qualityMetrics.completeness" 
                  :color="getQualityColor(qualityMetrics.completeness)"
                />
              </div>
              <div class="metric-item">
                <div class="metric-label">一致性</div>
                <el-progress 
                  :percentage="qualityMetrics.consistency" 
                  :color="getQualityColor(qualityMetrics.consistency)"
                />
              </div>
              <div class="metric-item">
                <div class="metric-label">准确性</div>
                <el-progress 
                  :percentage="qualityMetrics.accuracy" 
                  :color="getQualityColor(qualityMetrics.accuracy)"
                />
              </div>
              <div class="metric-item">
                <div class="metric-label">及时性</div>
                <el-progress 
                  :percentage="qualityMetrics.timeliness" 
                  :color="getQualityColor(qualityMetrics.timeliness)"
                />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 处理统计 -->
      <div class="processing-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="处理记录数" :value="processingStats.totalRecords" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="清洗后记录数" :value="processingStats.cleanedRecords" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="异常记录数" :value="processingStats.anomalousRecords" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="处理速度" :value="processingStats.processingSpeed" suffix="条/秒" />
          </el-col>
        </el-row>
      </div>

      <!-- 数据预览 -->
      <div class="data-preview">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="原始数据" name="raw">
            <el-table :data="rawData" style="width: 100%" max-height="400">
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="timestamp" label="时间戳" width="180" />
              <el-table-column prop="amount" label="金额" width="100" />
              <el-table-column prop="location" label="地点" width="120" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 'valid' ? 'success' : 'danger'">
                    {{ scope.row.status === 'valid' ? '有效' : '异常' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          
          <el-tab-pane label="清洗后数据" name="cleaned">
            <el-table :data="cleanedData" style="width: 100%" max-height="400">
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="timestamp" label="时间戳" width="180" />
              <el-table-column prop="amount_normalized" label="标准化金额" width="120" />
              <el-table-column prop="location_encoded" label="地点编码" width="120" />
              <el-table-column prop="hour" label="小时" width="80" />
              <el-table-column prop="is_weekend" label="周末" width="80">
                <template #default="scope">
                  <el-tag :type="scope.row.is_weekend ? 'warning' : 'info'">
                    {{ scope.row.is_weekend ? '是' : '否' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          
          <el-tab-pane label="特征统计" name="stats">
            <div class="feature-stats">
              <el-row :gutter="20">
                <el-col :span="12">
                  <h4>数值特征统计</h4>
                  <el-table :data="numericalStats" style="width: 100%">
                    <el-table-column prop="feature" label="特征" />
                    <el-table-column prop="mean" label="均值" />
                    <el-table-column prop="std" label="标准差" />
                    <el-table-column prop="min" label="最小值" />
                    <el-table-column prop="max" label="最大值" />
                  </el-table>
                </el-col>
                <el-col :span="12">
                  <h4>类别特征分布</h4>
                  <el-table :data="categoricalStats" style="width: 100%">
                    <el-table-column prop="feature" label="特征" />
                    <el-table-column prop="unique_count" label="唯一值数量" />
                    <el-table-column prop="most_frequent" label="最频繁值" />
                    <el-table-column prop="frequency" label="频率" />
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const isProcessing = ref(false)
const activeTab = ref('raw')

const preprocessConfig = reactive({
  missingValueStrategy: 'mean',
  outlierDetection: ['iqr'],
  duplicateHandling: 'keep_first',
  numericalFeatures: ['standardization', 'normalization'],
  categoricalFeatures: ['one_hot'],
  timeFeatures: ['hour', 'day_of_week', 'is_weekend']
})

const qualityMetrics = reactive({
  completeness: 85,
  consistency: 92,
  accuracy: 88,
  timeliness: 95
})

const processingStats = reactive({
  totalRecords: 0,
  cleanedRecords: 0,
  anomalousRecords: 0,
  processingSpeed: 0
})

const rawData = ref([
  { id: 1, timestamp: '2023-12-01 10:30:00', amount: 1500, location: '北京', status: 'valid' },
  { id: 2, timestamp: '2023-12-01 10:31:00', amount: null, location: '上海', status: 'invalid' },
  { id: 3, timestamp: '2023-12-01 10:32:00', amount: 2300, location: '广州', status: 'valid' },
  { id: 4, timestamp: '2023-12-01 10:33:00', amount: 50000, location: '深圳', status: 'anomaly' },
  { id: 5, timestamp: '2023-12-01 10:34:00', amount: 1800, location: '杭州', status: 'valid' }
])

const cleanedData = ref([
  { id: 1, timestamp: '2023-12-01 10:30:00', amount_normalized: 0.15, location_encoded: 1, hour: 10, is_weekend: false },
  { id: 3, timestamp: '2023-12-01 10:32:00', amount_normalized: 0.23, location_encoded: 2, hour: 10, is_weekend: false },
  { id: 5, timestamp: '2023-12-01 10:34:00', amount_normalized: 0.18, location_encoded: 3, hour: 10, is_weekend: false }
])

const numericalStats = ref([
  { feature: 'amount', mean: 1866.67, std: 756.23, min: 1500, max: 2300 },
  { feature: 'hour', mean: 10, std: 0, min: 10, max: 10 }
])

const categoricalStats = ref([
  { feature: 'location', unique_count: 3, most_frequent: '北京', frequency: 0.33 },
  { feature: 'is_weekend', unique_count: 1, most_frequent: false, frequency: 1.0 }
])

const getQualityColor = (percentage) => {
  if (percentage >= 90) return '#67C23A'
  if (percentage >= 70) return '#E6A23C'
  return '#F56C6C'
}

const startPreprocessing = async () => {
  if (isProcessing.value) return
  
  isProcessing.value = true
  ElMessage.info('开始数据预处理...')
  
  // 模拟预处理过程
  const totalRecords = 1000
  let processed = 0
  
  const processInterval = setInterval(() => {
    processed += Math.floor(Math.random() * 50) + 20
    processingStats.totalRecords = Math.min(processed, totalRecords)
    processingStats.cleanedRecords = Math.floor(processingStats.totalRecords * 0.85)
    processingStats.anomalousRecords = processingStats.totalRecords - processingStats.cleanedRecords
    processingStats.processingSpeed = Math.floor(Math.random() * 100) + 50
    
    // 更新质量指标
    qualityMetrics.completeness = Math.min(100, 80 + Math.floor(Math.random() * 20))
    qualityMetrics.consistency = Math.min(100, 85 + Math.floor(Math.random() * 15))
    qualityMetrics.accuracy = Math.min(100, 82 + Math.floor(Math.random() * 18))
    qualityMetrics.timeliness = Math.min(100, 90 + Math.floor(Math.random() * 10))
    
    if (processed >= totalRecords) {
      clearInterval(processInterval)
      isProcessing.value = false
      ElMessage.success('数据预处理完成！')
    }
  }, 500)
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quality-metrics {
  padding: 20px 0;
}

.metric-item {
  margin-bottom: 20px;
}

.metric-label {
  margin-bottom: 8px;
  font-weight: bold;
  color: #333;
}

.processing-stats {
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.data-preview {
  margin-top: 20px;
}

.feature-stats h4 {
  margin-bottom: 15px;
  color: #333;
}
</style>

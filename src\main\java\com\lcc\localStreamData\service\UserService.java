package com.lcc.localStreamData.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lcc.localStreamData.entity.User;

public interface UserService extends IService<User> {

    public Page<User> findList(int page, int pageSize, String username);

    public void active(int id);

    public void lock(int id);


    boolean isUsernameUnique(String username);
}

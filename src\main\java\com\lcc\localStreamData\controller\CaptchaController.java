package com.lcc.localStreamData.controller;

import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.google.code.kaptcha.util.Config;
import com.lcc.localStreamData.common.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Properties;

import static com.google.code.kaptcha.Constants.*;

@Slf4j
@RestController
@RequestMapping("/captcha")
public class CaptchaController {
    @GetMapping("/generate")
    public void generate(@RequestParam String uuid,
                         HttpServletRequest request, HttpServletResponse response) {
        //验证码生成器
        DefaultKaptcha captchaProducer = new DefaultKaptcha();
        Properties properties = new Properties();
        properties.setProperty(KAPTCHA_IMAGE_WIDTH, "160");
        properties.setProperty(KAPTCHA_IMAGE_HEIGHT, "60");
        Config config = new Config(properties);
        captchaProducer.setConfig(config);
        //生成验证码
        String code = captchaProducer.createText();
        BufferedImage image = captchaProducer.createImage(code);
//        log.info("uuid:{},code:{}", uuid, code);
        request.getSession().setAttribute(uuid, code);
        try {
            ImageIO.write(image, "jpg", response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}

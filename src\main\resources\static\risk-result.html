<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/elementui.css">
    <style>
        body {
            background-color: #fff;
        }
        #app {
            padding: 20px;
        }
        .page {
            margin-top: 30px;
            text-align: center;
        }
        .header {

        }
        .header .search {
            width: 230px;
        }
        .table {
            border: 2px solid #f3f4f7;
            margin-top: 20px;
        }
        .bread {
            margin-bottom: 20px;
        }
        .switch {
            margin-top: 8px;
        }
    </style>
</head>
<body>
<div id="app">

    <div class="bread">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item><a href="/">首页</a></el-breadcrumb-item>
            <el-breadcrumb-item><a href="risk.html">风险评估</a></el-breadcrumb-item>
            <el-breadcrumb-item><a href="/">评估结果</a></el-breadcrumb-item>
        </el-breadcrumb>
    </div>


    <div class="table">
        <el-table
                :data="tableData"
                stripe
                style="width: 100%"
                max-height="870">
            <el-table-column
                    type="index"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="streamTime"
                    label="消费时间"
                    width="320">
            </el-table-column>
            <el-table-column
                    prop="streamMoney"
                    label="消费金额(元)"
                    width="230">
            </el-table-column>
            <el-table-column
                    prop="streamSignLocation"
                    label="注册地"
                    width="250"
            >
            </el-table-column>
            <el-table-column
                    prop="streamConsumeLocation"
                    label="消费地"
                    width="320"
            >
            </el-table-column>
            <el-table-column
                    label="是否异常"
                    width="250"
            >
                <template slot-scope="scope">
                    <el-tag type="success" v-if="scope.row.streamIsNormal==1">正常消费</el-tag>
                    <el-tag type="danger" v-if="scope.row.streamIsNormal==0">异常消费</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    label="操作"
            >
                <template slot-scope="scope">
                    <el-button type="warning" icon="el-icon-warning-outline" circle @click="handleNormal(scope.row.streamId)" :disabled="scope.row.streamIsNormal==0"></el-button>
                    <el-button type="danger" icon="el-icon-delete" circle @click="handleDelete(scope.row.streamId)"></el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>

<!--    <div class="page">-->
<!--        <el-pagination-->
<!--                @size-change="handleSizeChange"-->
<!--                @current-change="handleCurrentChange"-->
<!--                :current-page="currentPage"-->
<!--                :page-sizes="[100, 200, 300, 400]"-->
<!--                :page-size="100"-->
<!--                layout="total, sizes, prev, pager, next, jumper"-->
<!--                :total="400">-->
<!--        </el-pagination>-->
<!--    </div>-->

</div>
</body>
<script src="js/vue.js"></script>
<script src="js/axios.js"></script>
<script src="js/elementui.js"></script>
<script src="js/moment.js"></script>
<script>
    new Vue({
        el: "#app",
        data() {
            return {
                tableData: [{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamMoney: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                }],
                currentPage: 1,
                condition: "",
                opt: "",
                dataIds:[]
            }
        },
        methods: {
            handleSizeChange(val) {
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                console.log(`当前页: ${val}`);
            },
            goBack() {
                console.log('go back');
            },
            getResultList() {
                let ids = localStorage.getItem("dataIds")
                this.dataIds = ids
                console.log(ids)
                localStorage.removeItem("dataIds")

                axios.post(`/risk/result`,{dataIds:ids}).then(response => {
                    let data = response.data.data
                    //格式转换
                    for (let i = 0; i < data.length; i++) {
                        data[i].streamTime = moment(data[i].streamTime).format('YYYY-MM-DD HH:mm:ss');
                        data[i].streamConsumeLocation = data[i].streamConsumeLocation.replaceAll(",","-")
                    }
                    this.tableData = data
                });
            },
            handleNormal(id) {
                this.$confirm('此操作将标记为异常消费, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {


                    axios.get(`/normal/mark?id=${id}`).then(response => {

                        if (response.data.code==1){
                            this.$message({
                                type: 'success',
                                message: '操作成功!'
                            });
                            this.refreshList()
                        }else {
                            this.$message.error('操作失败');
                        }

                    })


                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消标记'
                    });
                });
            },
            handleDelete(id) {
                this.$confirm('此操作将永久删除该消费, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error'
                }).then(() => {
                    axios.get(`/normal/delete?id=${id}`).then(response => {

                        if (response.data.code==1){
                            this.$message({
                                type: 'success',
                                message: '操作成功!'
                            });
                            this.refreshList()
                        }else {
                            this.$message.error('操作失败');
                        }


                    })
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            },
            refreshList() {
                axios.post(`/risk/result`,{dataIds:this.dataIds}).then(response => {
                    let data = response.data.data
                    //格式转换
                    for (let i = 0; i < data.length; i++) {
                        data[i].streamTime = moment(data[i].streamTime).format('YYYY-MM-DD HH:mm:ss');
                        data[i].streamConsumeLocation = data[i].streamConsumeLocation.replaceAll(",","-")
                    }
                    this.tableData = data
                });
            }
        },
        created() {
            this.getResultList()
        }
    });
</script>
</html>
package com.lcc.localStreamData.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcc.localStreamData.common.R;
import com.lcc.localStreamData.entity.Dataset;
import com.lcc.localStreamData.service.DatasetService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;

@Slf4j
@RestController
@RequestMapping("/dataset")
public class DatasetController {

    @Autowired
    DatasetService datasetService;


    @GetMapping("/list")
    public R<Page<Dataset>> list(int page, int pageSize, String datasetName) {
        Page<Dataset> list = datasetService.findList(page, pageSize, datasetName);
        return R.success(list);
    }

    @PostMapping
    public R<String> save(@RequestBody Dataset dataset) {
        dataset.setCreateTime(LocalDateTime.now());
        //权限和创建用户先默认
        dataset.setPermission("000");
        dataset.setCreateUser(1);
        //预处理状态默认未处理
        dataset.setStatus("0");
        datasetService.save(dataset);
        return R.success("");
    }

    @DeleteMapping("/{id}")
    public R<String> delete(@PathVariable Integer id) {
        //获取数据集对应的文件名
        Dataset dataset = datasetService.getById(id);
        //删除记录
        datasetService.removeById(id);
        //返回文件名
        return R.success(dataset.getFilename());
    }

}

package com.lcc.localStreamData.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lcc.localStreamData.entity.Dataset;
import com.lcc.localStreamData.entity.Rule;
import com.lcc.localStreamData.entity.RuleGroup;
import com.lcc.localStreamData.entity.vo.RuleGroupVo;
import com.lcc.localStreamData.mapper.RuleGroupMapper;
import com.lcc.localStreamData.mapper.RuleMapper;
import com.lcc.localStreamData.service.RuleGroupService;
import com.lcc.localStreamData.service.RuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RuleGroupServiceImpl extends ServiceImpl<RuleGroupMapper, RuleGroup> implements RuleGroupService {
    @Autowired
    RuleService ruleService;

    @Override
    public Page<RuleGroupVo> findPage(int page, int pageSize, String name) {
        Page<RuleGroup> pageInfo = new Page<>(page, pageSize);
        //构造条件构造器
        QueryWrapper<RuleGroup> wrapper = new QueryWrapper<>();
        wrapper.like(StringUtils.isNotEmpty(name), "name", name);
        Page<RuleGroup> ruleGroupPage = this.page(pageInfo, wrapper);
        //子查询
        List<RuleGroupVo> ruleGroupVoList = ruleGroupPage.getRecords().stream().map(ruleGroup -> {
            Integer ruleGroupId = ruleGroup.getId();
            List<Rule> ruleList = ruleService.findListByRuleGroupId(ruleGroupId);
            RuleGroupVo ruleGroupVo = new RuleGroupVo();
            BeanUtils.copyProperties(ruleGroup, ruleGroupVo);
            ruleGroupVo.setRuleList(ruleList);
            return ruleGroupVo;
        }).collect(Collectors.toList());
        //构造分页结果
        Page<RuleGroupVo> result = new Page<>();
        BeanUtils.copyProperties(ruleGroupPage, result);
        result.setRecords(ruleGroupVoList);
        return result;
    }

    @Override
    @Transactional
    public void saveRuleGroup(String name, List<Rule> ruleList) {
        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setName(name);
        ruleGroup.setCreateUser(1);
        ruleGroup.setCreateTime(LocalDateTime.now());
        //保存ruleGroup
        this.save(ruleGroup);
        //设置规则所属组
        ruleList.forEach(rule -> rule.setRuleGroupId(ruleGroup.getId()));
        //保存
        ruleService.saveBatch(ruleList);
    }

    @Override
    @Transactional
    public void removeRuleGroup(Integer id) {
        //删除规则组中的规则
        QueryWrapper<Rule> wrapper = new QueryWrapper<>();
        wrapper.eq("rule_group_id", id);
        ruleService.remove(wrapper);
        //删除规则组
        this.removeById(id);
    }

    @Override
    public RuleGroupVo getRuleGroup(Integer id) {
        RuleGroup ruleGroup = this.getById(id);
        List<Rule> ruleList = ruleService.findListByRuleGroupId(id);
        RuleGroupVo ruleGroupVo = new RuleGroupVo();
        BeanUtils.copyProperties(ruleGroup, ruleGroupVo);
        ruleGroupVo.setRuleList(ruleList);
        return ruleGroupVo;
    }
}

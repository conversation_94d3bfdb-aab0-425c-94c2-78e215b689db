#!/user/bin/env python3
# -*- coding: utf-8 -*-
# from hdfs import InsecureClient
# from dataclasses import dataclass
import pymysql
import csv
import pandas as pd
from flask import Flask, request, url_for, redirect, render_template, jsonify, send_from_directory
from imblearn.over_sampling import SMOTE
from sklearn.impute import KNNImputer
from sklearn.preprocessing import StandardScaler, MinMaxScaler

app = Flask(__name__)
import warnings

warnings.filterwarnings("ignore")
import joblib
import datetime
import mysql.connector
import time
import random
import json
import collections
import random
import matplotlib.pyplot as plt
import os
import copy
from sklearn.metrics import confusion_matrix, classification_report
from sklearn.model_selection import train_test_split
import numpy as np
import pandas as pd
import xgboost as xgb
import pickle
import lightgbm as lgb
from joblib import dump, load
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.svm import LinearSVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import accuracy_score
from sqlalchemy import create_engine
from werkzeug.utils import secure_filename
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

new_config = {
    "host": "127.0.0.1",
    "port": 3307,
    "user": "root",
    "password": "1234",
    "database": "localstreamdata"
}


# 读取原始MySQL数据
def get_data():
    cnx = mysql.connector.connect(user='root', password='123456', host='localhost', database=new_config["database"])
    cursor = cnx.cursor()
    # 执行查询 normal_data
    query = "SELECT * FROM normal_data"
    cursor.execute(query)
    rows = cursor.fetchall()
    column_names = [desc[0] for desc in cursor.description]  # 获取列名
    normal_data = pd.DataFrame(rows, columns=column_names)
    # 执行查询 train_data_process
    query = "SELECT * FROM train_data_process"
    cursor.execute(query)
    rows = cursor.fetchall()
    column_names = [desc[0] for desc in cursor.description]  # 获取列名
    train_data_process = pd.DataFrame(rows, columns=column_names)
    # 关闭游标和数据库连接
    cursor.close()
    cnx.close()
    return normal_data, train_data_process


# 生成策略1
def cl_1(data):
    # 60秒内消费3次。
    data_q10 = data.copy()
    data_h10 = data.copy()
    data_q10['stream_time'] = data['stream_time'] - pd.Timedelta(seconds=10)
    data_h10['stream_time'] = data['stream_time'] + pd.Timedelta(seconds=10)
    data = pd.concat([data_q10, data], axis=0)
    data = pd.concat([data, data_h10], axis=0)
    data = data.sort_values(by=['stream_id'], ascending=[True])
    data = data.reset_index()
    data = data.drop(['index'], axis=1)
    return data


# 生成策略2
def get_random_category(seed):
    """ 根据随机种子返回一个随机的地址类别 """
    return unique_categories[seed]


def cl_2(data, unique_categories):
    # 异地消费间隔，比如10分钟内，在两个不同地方消费。
    data_q10 = data.copy()
    unique_categories = unique_categories
    data_q10['stream_time'] = data['stream_time'] + pd.Timedelta(minutes=5)
    # 为每行生成一个随机种子
    np.random.seed(123)
    random_seeds = np.random.randint(0, len(unique_categories), size=len(data))
    # print(len(data))
    # print(len(unique_categories))
    # print(random_seeds)
    data_q10['stream_consume_location'] = [get_random_category(seed) for seed in random_seeds]
    # 合并
    data = pd.concat([data_q10, data], axis=0)
    data = data.sort_values(by=['stream_id'], ascending=[True])
    data = data.reset_index()
    data = data.drop(['index'], axis=1)
    return data


# 生成策略3
def cl_3(data):
    # 消费频次，比如10分钟内，总共消费5次算消费异常。
    data_1fz = data.copy()
    data_2fz = data.copy()
    data_3fz = data.copy()
    data_4fz = data.copy()
    data_5fz = data.copy()
    data_1fz['stream_time'] = data['stream_time'] + pd.Timedelta(minutes=1)
    data_2fz['stream_time'] = data['stream_time'] + pd.Timedelta(minutes=2)
    data_3fz['stream_time'] = data['stream_time'] + pd.Timedelta(minutes=3)
    data_4fz['stream_time'] = data['stream_time'] + pd.Timedelta(minutes=4)
    data_1fz['stream_money'] = data['stream_money'] + np.random.randint(0, 1000, size=len(data))
    data_2fz['stream_money'] = data['stream_money'] + np.random.randint(0, 1000, size=len(data))
    data_3fz['stream_money'] = data['stream_money'] + np.random.randint(0, 1000, size=len(data))
    data_4fz['stream_money'] = data['stream_money'] + np.random.randint(0, 1000, size=len(data))
    data = pd.concat([data, data_1fz], axis=0)
    data = pd.concat([data, data_2fz], axis=0)
    data = pd.concat([data, data_3fz], axis=0)
    data = pd.concat([data, data_4fz], axis=0)
    data = data.sort_values(by=['stream_id'], ascending=[True])
    data = data.reset_index()
    data = data.drop(['index'], axis=1)
    return data


# 判定异常策略1
def pd_1(TestData_cl1):
    if TestData_cl1.empty:
        print('TestData_cl1为空')
        return TestData_cl1
    else:
        # （1）异常消费规则1：根据消费间隔，比如60秒内，消费次数达到3次算异常。
        TestData_cl1 = TestData_cl1.sort_values(['stream_user_id', 'stream_consume_type', 'stream_seconds'])
        TestData_cl1['时间差'] = TestData_cl1["stream_time"].diff().dt.total_seconds().iloc[1:]
        TestData_cl1["下一行时间点"] = TestData_cl1["stream_time"].shift(-1)
        TestData_cl1["时间差2"] = TestData_cl1["下一行时间点"].sub(TestData_cl1["stream_time"])
        TestData_cl1["时间差2"] = TestData_cl1["时间差2"].apply(lambda x: x.total_seconds())
        TestData_cl1[["时间差2", "时间差"]] = TestData_cl1[["时间差2", "时间差"]].apply(abs)
        TestData_cl1["异常"] = TestData_cl1[["时间差2", "时间差"]].apply(lambda x: 1 if x.min() < 60 else 0, axis=1)
        TestData_cl1 = TestData_cl1.drop(["下一行时间点", '时间差', '时间差2'], axis=1)
        TestData_cl1['是否新模拟'] = 1
        return TestData_cl1


# 判定异常策略2
def pd_2(df):
    if df.empty:
        print('df为空')
        return df
    else:
        # （2）异常消费规则2：根据异地消费间隔，比如10分钟内，在两个不同地点消费算异常。
        df.sort_values(by=['stream_user_id', 'stream_consume_type', 'stream_seconds'], inplace=True)
        df['stream_time'] = pd.to_datetime(df['stream_time'])
        # 计算相邻两行之间的时间差，单位为秒
        df['时间差'] = df['stream_time'].shift(1) - df['stream_time']
        df['时间差'] = df['时间差'].dt.seconds
        df.fillna(value={"时间差": 60}, limit=1, inplace=True)
        df["下一行时间点"] = df["stream_time"].shift(-1)
        df["时间差2"] = df["下一行时间点"].sub(df["stream_time"])
        df["时间差2"] = df["时间差2"].apply(lambda x: x.total_seconds())
        df[["时间差2", "时间差"]] = df[["时间差2", "时间差"]].apply(abs)
        df['真时间差'] = np.minimum(df['时间差'], df['时间差2'])
        df['时间差'] = df['真时间差']
        # 对 ['消费者id', '消费类型id'] 进行分组，并计算每个分组中的唯一消费地点数量
        grouped = df.groupby(['stream_user_id', 'stream_consume_type', 'stream_seconds'])[
            'stream_consume_location'].nunique().reset_index(name='10分钟内消费地点数')
        # 判断唯一消费地点数量不为 1 的记录，将结果标记为异常
        df = df.merge(grouped[['stream_user_id', 'stream_consume_type', 'stream_seconds', '10分钟内消费地点数']],
                      on=['stream_user_id', 'stream_consume_type', 'stream_seconds'], how='left')
        df['是否异地消费'] = np.where(df['10分钟内消费地点数'] != 1, True, False)
        df['异常'] = np.where(df['是否异地消费'], 1, 0)
        df = df.drop(["下一行时间点", '时间差2', '真时间差', '时间差', '10分钟内消费地点数', '是否异地消费'], axis=1)
        df['是否新模拟'] = 1
        return df


# 判定异常策略3
def pd_3(TestData_cl3):
    if TestData_cl3.empty:
        print('TestData_cl3为空')
        return TestData_cl3
    else:
        # （3）异常消费规则3：根据消费频次，比如10分钟内，总共消费5次算消费频次异常。
        TestData_cl3.sort_values(by=['stream_user_id', 'stream_consume_type', 'stream_seconds'], inplace=True)
        # 将时间列的数据类型转为 datetime
        TestData_cl3['stream_time'] = pd.to_datetime(TestData_cl3['stream_time'])
        # 计算相邻两行之间的时间差，单位为秒
        TestData_cl3['时间差'] = TestData_cl3['stream_time'].diff().dt.seconds
        TestData_cl3.fillna(value={"时间差": 60}, limit=1, inplace=True)
        TestData_cl3["下一行时间点"] = TestData_cl3["stream_time"].shift(-1)
        TestData_cl3["时间差2"] = TestData_cl3["下一行时间点"].sub(TestData_cl3["stream_time"])
        TestData_cl3["时间差2"] = TestData_cl3["时间差2"].apply(lambda x: x.total_seconds())
        TestData_cl3[["时间差2", "时间差"]] = TestData_cl3[["时间差2", "时间差"]].apply(abs)
        TestData_cl3['真时间差'] = np.minimum(TestData_cl3['时间差'], TestData_cl3['时间差2'])
        TestData_cl3['时间差'] = TestData_cl3['真时间差']

        # 按 ['消费者id', '消费类型id', '秒'] 进行分组，并统计组内的时间差小于600的数量
        def check_abnormal(group):
            return (group['时间差'] < 600).sum() >= 1

        result = TestData_cl3.groupby(['stream_user_id', 'stream_consume_type', 'stream_seconds']).apply(
            check_abnormal).replace(True, 1).replace(False, 0)
        result = result.reset_index().rename(columns={0: '异常'})
        TestData_cl3 = TestData_cl3.merge(result, on=['stream_user_id', 'stream_consume_type', 'stream_seconds'])
        TestData_cl3 = TestData_cl3.drop(["下一行时间点", '时间差2', '真时间差', '时间差'], axis=1)
        TestData_cl3['是否新模拟'] = 1
        return TestData_cl3


# 根据需求、提取特征
def tz(TestData_cl_copy):
    # 构建样本特征集F。比如：样本特征包括：样本ID、当前时间（秒数）、时间间隔（秒数）、消费总金额、消费总频次、注册地、消费地1、消费地2、消费地3、消费地4、60秒内的消费次数、10分钟内的消费地点个数、10分钟内消费次数、消费者注册地与消费地是否一致等。（如果没有多个消费地，那剩下的消费地可以为空或者为0等等）
    # 样本ID、消费地1、消费地2、消费地3、消费地4等。（如果没有多个消费地，那剩下的消费地可以为空或者为0等等）
    # '消费者id'、消费总频次、'消费类型id'、当前时间（秒数）、时间间隔（秒数）、消费总金额、注册地、60秒内的消费次数、消费者注册地与消费地是否一致、10分钟内消费次数、10分钟内的消费地点个数
    # 真时间差,60消费次数
    # 真时间差,60消费次数   （判定1）
    TestData_cl_copy = TestData_cl_copy.sort_values(
        ["stream_user_id", "stream_consume_type", "stream_seconds", 'stream_time'])
    TestData_cl_copy['stream_time'] = pd.to_datetime(TestData_cl_copy['stream_time'])
    TestData_cl_copy['时间差'] = TestData_cl_copy['stream_time'].diff().dt.seconds
    TestData_cl_copy.fillna(value={"时间差": 60}, limit=1, inplace=True)
    TestData_cl_copy["下一行时间点"] = TestData_cl_copy["stream_time"].shift(-1)
    TestData_cl_copy["时间差2"] = TestData_cl_copy["下一行时间点"].sub(TestData_cl_copy["stream_time"])
    TestData_cl_copy["时间差2"] = TestData_cl_copy["时间差2"].apply(lambda x: x.total_seconds())
    TestData_cl_copy = TestData_cl_copy.drop("下一行时间点", axis=1)
    TestData_cl_copy[["时间差2", "时间差"]] = TestData_cl_copy[["时间差2", "时间差"]].apply(abs)
    TestData_cl_copy['真时间差'] = np.minimum(TestData_cl_copy['时间差'], TestData_cl_copy['时间差2'])
    TestData_cl_copy['时间差'] = TestData_cl_copy['真时间差']
    TestData_cl_copy = TestData_cl_copy.drop(['时间差2', '真时间差'], axis=1)
    TestData_cl_copy.fillna(value={"真时间差": 60}, limit=1, inplace=True)
    TestData_cl_copy['是否间隔60消费'] = TestData_cl_copy.apply(lambda row: 1 if row['时间差'] <= 60 else 0, axis=1)
    TestData_cl_copy['60消费次数'] = \
        TestData_cl_copy.groupby(['stream_user_id', 'stream_consume_type', 'stream_seconds'])[
            '是否间隔60消费'].transform(
            'sum')
    # 10分钟内的消费地点个数   （判定2）
    grouped = TestData_cl_copy.groupby(['stream_user_id', 'stream_consume_type', 'stream_seconds'])[
        'stream_consume_location'].nunique().reset_index(name='消费者id10分钟内消费地点数')
    TestData_cl_copy = TestData_cl_copy.merge(
        grouped[['stream_user_id', 'stream_consume_type', 'stream_seconds', '消费者id10分钟内消费地点数']],
        on=['stream_user_id', 'stream_consume_type', 'stream_seconds'], how='left')

    # 10分钟内消费次数   （判定3）
    def check_abnormal(group):
        return (group['时间差'] < 600).sum()

    result = TestData_cl_copy.groupby(['stream_user_id', 'stream_consume_type', 'stream_seconds']).apply(check_abnormal)
    result = result.reset_index().rename(columns={0: '10分钟内消费次数'})
    TestData_cl_copy = TestData_cl_copy.merge(result, on=['stream_user_id', 'stream_consume_type', 'stream_seconds'])
    TestData_cl_copy = TestData_cl_copy.drop(['时间差', '是否间隔60消费'], axis=1)
    return TestData_cl_copy


def data_td(normal_data, unique_categories):
    '''
    # MySQL读取数据
    normal_data, train_data_process = get_data()
    train_data_process = train_data_process.iloc
    unique_categories = train_data_process['stream_consume_location'].unique()
    unique_categories2 = normal_data['stream_consume_location'].unique()
    #print(unique_categories)
    #print(unique_categories2)
    #print(len(unique_categories))
    #print(len(unique_categories2))
    unique_categories = np.unique(np.concatenate((unique_categories, unique_categories2), axis=None))
    #print(len(unique_categories))
    train_data_process = train_data_process.iloc[:600]
    normal_data = normal_data.iloc[:600]
    '''
    # 数据划分，要保证每6s内，异常和正常都有
    normal_data['minutes_per_second'] = normal_data['stream_time'].dt.second % 6
    TestData_cl1 = normal_data[(normal_data['minutes_per_second'] >= 0) & (normal_data['minutes_per_second'] < 1)]
    TestData_cl2 = normal_data[(normal_data['minutes_per_second'] >= 1) & (normal_data['minutes_per_second'] < 2)]
    TestData_cl3 = normal_data[(normal_data['minutes_per_second'] >= 2) & (normal_data['minutes_per_second'] < 3)]
    TestData_data = normal_data[(normal_data['minutes_per_second'] >= 3)]
    print('111111111111111111111111111111222222222222222222222222222222222999999999999999999999999999999')
    print('TestData_cl1')
    print(TestData_cl1)
    print('TestData_cl2')
    print(TestData_cl2)
    print('TestData_cl3')
    print(TestData_cl3)
    print('TestData_data')
    print(TestData_data)

    # 新增模拟数据
    # TestData_cl1 = cl_1(TestData_cl1)
    # TestData_cl2 = cl_2(TestData_cl2, unique_categories)
    # TestData_cl3 = cl_3(TestData_cl3)
    # 异常消费判定
    TestData_cl1 = pd_1(TestData_cl1)
    TestData_cl2 = pd_2(TestData_cl2)
    TestData_cl3 = pd_3(TestData_cl3)

    # 合并3种数据集
    # test
    TestData_data = TestData_data.reset_index()
    TestData_data['异常'] = 0
    TestData_data['是否新模拟'] = 0
    test = pd.concat([TestData_cl1, TestData_cl2, TestData_cl3, TestData_data], axis=0)
    test = tz(test)
    test = test.sort_values('stream_time', ascending=True)
    test = test.reset_index()
    test = test.drop(test.columns[0], axis=1)
    test = test.drop(["index", "stream_is_new", "stream_is_normal"], axis=1)
    test = test.drop(['stream_consume_location', 'stream_sign_location', 'stream_time_minute', 'stream_time'], axis=1)
    test = test.sort_values(by=['stream_id'], ascending=[True])
    return test


def train_model_xgb(train, modelName, id, modelDescription, trainer, trainerid, modelType):
    train = train.drop(['stream_id'], axis=1)
    print('train.dtypes预测的数据类型')
    print(train.dtypes)
    print("%%%%训练_model_xgb%%%%")
    params = {'booster': 'gbtree',
              'objective': 'binary:logistic',
              'eval_metric': 'auc',
              'silent': 1,
              'eta': 0.01,
              'max_depth': 7,
              'min_child_weight': 5,
              'gamma': 0.2,
              'lambda': 1,
              'colsample_bylevel': 0.7,
              'colsample_bytree': 0.8,
              'subsample': 0.8,
              'scale_pos_weight': 1}
    dtrain = xgb.DMatrix(train.drop(['异常'], axis=1), label=train['异常'])
    watchlist = [(dtrain, 'train')]
    model = xgb.train(params, dtrain, num_boost_round=300, evals=watchlist)
    # 连接到MySQL数据库
    # connection = pymysql.connect(host='************', user='ubuntu', password='1', db='localstreamdata')
    connection = pymysql.connect(host=new_config["host"], port=new_config["port"],
                                 user=new_config["user"], password=new_config["password"], db=new_config["database"])
    cursor = connection.cursor()
    current_time = datetime.datetime.now()
    time = current_time
    id = id
    model_name = modelName + "(" + "xgb" + ")"
    model_description = modelDescription
    trainer = trainer
    trainer_id = trainerid
    training_parameters = joblib.dump(model, 'xgb_model.pkl')
    sql = "INSERT INTO models (time,id, model_name, model_description, trainer_id, trainer, model_type, training_parameters) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)"
    cursor.execute(sql, (time, id, model_name, model_description, trainer_id, trainer, modelType, training_parameters))
    connection.commit()
    connection.close()
    return id


def train_model_gbdt(train, modelName, id, modelDescription, trainer, trainerid, modelType):
    train = train.drop(['stream_id'], axis=1)
    print('train.dtypes预测的数据类型')
    print(train.dtypes)
    print("%%%%训练_model_gbdt%%%%")
    gbdt_model = GradientBoostingClassifier(n_estimators=100, learning_rate=0.1, max_depth=3, random_state=42)
    X_train = train.drop(['异常'], axis=1)
    y_train = train['异常']
    gbdt_model.fit(X_train, y_train)
    # 连接到MySQL数据库
    # connection = pymysql.connect(host='************', user='ubuntu', password='1', db='localstreamdata')
    connection = pymysql.connect(host=new_config["host"], port=new_config["port"],
                                 user=new_config["user"], password=new_config["password"], db=new_config["database"])
    cursor = connection.cursor()
    current_time = datetime.datetime.now()
    time = current_time
    id = id
    model_name = modelName + "(" + "gbdt" + ")"
    model_description = modelDescription
    trainer = trainer
    trainer_id = trainerid
    training_parameters = joblib.dump(gbdt_model, 'gbdt_model.pkl')
    sql = "INSERT INTO models (time,id, model_name, model_description, trainer_id, trainer,model_type, training_parameters) VALUES (%s, %s, %s, %s, %s, %s, %s,%s)"
    cursor.execute(sql, (time, id, model_name, model_description, trainer_id, trainer, modelType, training_parameters))
    connection.commit()
    connection.close()
    return id


def train_model_lr(train, modelName, id, modelDescription, trainer, trainerid, modelType):
    train = train.drop(['stream_id'], axis=1)
    print('train.dtypes预测的数据类型')
    print(train.dtypes)
    print("%%%%训练_model_lr%%%%")
    lr_model = LogisticRegression(random_state=42)
    X_train = train.drop(['异常'], axis=1)
    y_train = train['异常']
    lr_model.fit(X_train, y_train)
    # 连接到MySQL数据库
    # connection = pymysql.connect(host='************', user='ubuntu', password='1', db='localstreamdata')
    connection = pymysql.connect(host=new_config["host"], port=new_config["port"],
                                 user=new_config["user"], password=new_config["password"], db=new_config["database"])
    cursor = connection.cursor()
    current_time = datetime.datetime.now()
    time = current_time
    id = id
    model_name = modelName + "(" + "lr" + ")"
    model_description = modelDescription
    trainer = trainer
    trainer_id = trainerid
    training_parameters = joblib.dump(lr_model, 'lr_model.pkl')
    sql = "INSERT INTO models (time,id, model_name, model_description, trainer_id, trainer,model_type, training_parameters) VALUES (%s, %s, %s, %s, %s, %s, %s,%s)"
    cursor.execute(sql, (time, id, model_name, model_description, trainer_id, trainer, modelType, training_parameters))
    connection.commit()
    connection.close()
    return id


def train_model_svm(train, modelName, id, modelDescription, trainer, trainerid, modelType):
    train = train.drop(['stream_id'], axis=1)
    print('train.dtypes预测的数据类型')
    print(train.dtypes)
    print("%%%%训练_model_svm%%%%")
    svm_model = LinearSVC(C=1.0, random_state=42)
    X_train = train.drop(['异常'], axis=1)
    y_train = train['异常']
    svm_model.fit(X_train, y_train)
    # 连接到MySQL数据库
    # connection = pymysql.connect(host='************', user='ubuntu', password='1', db='localstreamdata')
    connection = pymysql.connect(host=new_config["host"], port=new_config["port"],
                                 user=new_config["user"], password=new_config["password"], db=new_config["database"])
    cursor = connection.cursor()
    current_time = datetime.datetime.now()
    time = current_time
    id = id
    model_name = modelName + "(" + "svm" + ")"
    model_description = modelDescription
    trainer = trainer
    trainer_id = trainerid
    training_parameters = joblib.dump(svm_model, 'svm_model.pkl')
    sql = "INSERT INTO models (time,id, model_name, model_description, trainer_id, trainer,model_type, training_parameters) VALUES (%s, %s, %s, %s, %s, %s, %s,%s)"
    cursor.execute(sql, (time, id, model_name, model_description, trainer_id, trainer, modelType, training_parameters))
    connection.commit()
    connection.close()
    return id


def train_model_rf(train, modelName, id, modelDescription, trainer, trainerid, modelType):
    train = train.drop(['stream_id'], axis=1)
    print('train.dtypes预测的数据类型')
    print(train.dtypes)
    print("%%%%训练_model_rf%%%%")
    rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
    X_train = train.drop(['异常'], axis=1)
    y_train = train['异常']
    rf_model.fit(X_train, y_train)
    # 连接到MySQL数据库
    # connection = pymysql.connect(host='************', user='ubuntu', password='1', db='localstreamdata')
    connection = pymysql.connect(host=new_config["host"], port=new_config["port"],
                                 user=new_config["user"], password=new_config["password"], db=new_config["database"])
    cursor = connection.cursor()
    current_time = datetime.datetime.now()
    time = current_time
    id = id
    model_name = modelName + "(" + "rf" + ")"
    model_description = modelDescription
    trainer = trainer
    trainer_id = trainerid
    training_parameters = joblib.dump(rf_model, 'rf_model.pkl')
    sql = "INSERT INTO models (time,id, model_name, model_description, trainer_id, trainer,model_type, training_parameters) VALUES (%s, %s, %s, %s, %s, %s, %s,%s)"
    cursor.execute(sql, (time, id, model_name, model_description, trainer_id, trainer, modelType, training_parameters))
    connection.commit()
    connection.close()
    return id


def train_model_cart(train, modelName, id, modelDescription, trainer, trainerid, modelType):
    train = train.drop(['stream_id'], axis=1)
    print('train.dtypes预测的数据类型')
    print(train.dtypes)
    print("%%%%训练_model_cart%%%%")
    cart_model = DecisionTreeClassifier(criterion='gini', max_depth=5, random_state=42)
    X_train = train.drop(['异常'], axis=1)
    y_train = train['异常']
    cart_model.fit(X_train, y_train)
    # 连接到MySQL数据库
    # connection = pymysql.connect(host='************', user='ubuntu', password='1', db='localstreamdata')
    connection = pymysql.connect(host=new_config["host"], port=new_config["port"],
                                 user=new_config["user"], password=new_config["password"], db=new_config["database"])
    cursor = connection.cursor()
    current_time = datetime.datetime.now()
    time = current_time
    id = id
    model_name = modelName + "(" + "cart" + ")"
    model_description = modelDescription
    trainer = trainer
    trainer_id = trainerid
    training_parameters = joblib.dump(cart_model, 'cart_model.pkl')
    sql = "INSERT INTO models (time,id, model_name, model_description, trainer_id, trainer,model_type, training_parameters) VALUES (%s, %s, %s, %s, %s, %s, %s,%s)"
    cursor.execute(sql, (time, id, model_name, model_description, trainer_id, trainer, modelType, training_parameters))
    connection.commit()
    connection.close()
    return id


# 读取原始MySQL数据
def get_data():
    cnx = mysql.connector.connect(user='ubuntu', password='1', host='************', database='localstreamdata')
    cursor = cnx.cursor()
    query = "SELECT * FROM test"
    cursor.execute(query)
    rows = cursor.fetchall()
    column_names = [desc[0] for desc in cursor.description]
    normal_data = pd.DataFrame(rows, columns=column_names)
    query = "SELECT * FROM train"
    cursor.execute(query)
    rows = cursor.fetchall()
    column_names = [desc[0] for desc in cursor.description]
    train_data_process = pd.DataFrame(rows, columns=column_names)
    cursor.close()
    cnx.close()
    return normal_data, train_data_process


############################################################主页
@app.route('/', methods=['get', 'post'])
def register_post_list():
    return render_template("系统_配电网主页.html")


@app.route('/sc')
def index():
    return render_template('系统_配电网主页.html')


@app.route('/cool_form', methods=['GET', 'POST'])
def cool_form():
    if request.method == 'POST':
        return redirect(url_for('index'))
    return render_template('系统_配电网主页.html')


############################################################模型训练
@app.route('/xl', methods=['GET', 'POST'])
def xl():
    if request.method == 'POST':
        return redirect(url_for('index'))
    return render_template('模型训练.html')


@app.route("/model/train", methods=["POST"])
def model_train():
    ################################# 创建models表
    # engine = create_engine('mysql+pymysql://root:1234@127.0.0.1:3307/localstreamdata')
    engine = create_engine('mysql+pymysql://' + new_config["user"] + ':' + new_config["password"]
                           + '@' + new_config["host"] + ':' + str(new_config["port"]) + '/' + new_config["database"])
    models = 'models'
    with engine.connect() as connection:
        exists = connection.execute(f"SHOW TABLES LIKE '{models}'").fetchall()
    if not exists:
        sql_create_table = f'''
        CREATE TABLE {models} (
          number INT AUTO_INCREMENT PRIMARY KEY,
          time DATETIME,
          id INT ,
          model_name VARCHAR(255),
          model_description VARCHAR(255),
          trainer_id INT ,
          trainer VARCHAR(255),
          training_parameters BLOB
        );
        '''
        with engine.connect() as connection:
            connection.execute(sql_create_table)
    ################################# 接收用户通过post形式发送的数据
    print(request.form)
    modelName = request.form.get("modelName")
    id = request.form.get("id")
    id = int(id)
    modelDescription = request.form.get("modelDescription")
    dataUrl = request.form.get("dataUrl")
    trainerid = request.form.get("trainer")
    trainerid = int(trainerid)
    user = 'user'
    with engine.connect() as connection:
        trainerid = trainerid  # 假设要查询的trainerid为1
        query = f"SELECT username,user_level FROM {user} WHERE id = {trainerid}"
        result = connection.execute(query).fetchone()
        if result:
            trainer = result[0]
            level = result[1]
            print(trainer)
        else:
            print("Trainer not found.")

    df = pd.read_csv(dataUrl)
    df = df.drop(df.columns[0], axis=1)
    modelId = id
    if modelId == 1:
        results_xgb = train_model_xgb(df, modelName, id, modelDescription, trainer, trainerid, level)
    elif modelId == 2:
        results_gbdt = train_model_gbdt(df, modelName, id, modelDescription, trainer, trainerid, level)
    elif modelId == 3:
        results_lr = train_model_lr(df, modelName, id, modelDescription, trainer, trainerid, level)
    elif modelId == 4:
        results_svm = train_model_svm(df, modelName, id, modelDescription, trainer, trainerid, level)
    elif modelId == 5:
        results_rf = train_model_rf(df, modelName, id, modelDescription, trainer, trainerid, level)
    elif modelId == 6:
        results_cart = train_model_cart(df, modelName, id, modelDescription, trainer, trainerid, level)
    else:
        print("无效的 modelId")
    print("%%%%训练完%%%%")
    # return render_template('系统_运行.html')
    return {
        "code": 1,
        "msg": "",
        "data": ""
    }


############################################################模型预测
@app.route('/yc', methods=['GET', 'POST'])
def yc():
    if request.method == 'POST':
        return redirect(url_for('index'))
    return render_template('模型预测.html')


@app.route("/model/test", methods=["POST"])
def model_test():
    ################################# 接收用户通过post形式发送的数据
    print(request.form)
    dataIds = request.form.get("dataIds")
    modelId = request.form.get("modelId")
    modelId = int(modelId)
    print(dataIds)
    print(modelId)
    data_ids = [int(id) for id in dataIds.split(",")]
    ################################# 在MySQL中读取指定行转为DataFrame
    # cnx = mysql.connector.connect(user='ubuntu', password='1', host='************', database='localstreamdata')
    cnx = mysql.connector.connect(user=new_config["user"], password=new_config["password"], host=new_config["host"],
                                  port=new_config["port"], database=new_config["database"])
    cursor = cnx.cursor()
    query = "SELECT * FROM stream_data"
    cursor.execute(query)
    rows = cursor.fetchall()
    column_names = [desc[0] for desc in cursor.description]
    normal_data = pd.DataFrame(rows, columns=column_names)
    cursor.close()
    cnx.close()
    # normal_data["stream_id"] = normal_data["stream_id"].astype(str)
    print('stream_data--------------------------------------------------')
    print(normal_data)
    print(normal_data.dtypes)
    print('data_ids--------------------------------------------------')
    print(data_ids)
    print(type(data_ids[0]))
    print(
        'normal_data[normal_data["stream_id"].apply(lambda x: str(x) in data_ids)]--------------------------------------------------')
    test = normal_data[normal_data["stream_id"].apply(lambda x: int(x) in data_ids)]
    # print(normal_data["stream_id"].apply(lambda x: str(x) in data_ids))
    # print(test)
    test = pd.DataFrame(columns=normal_data.columns)  # 创建一个空的DataFrame，用于存储符合条件的行
    a = normal_data["stream_id"]
    for index, row in normal_data.iterrows():
        if int(row["stream_id"]) in data_ids:
            test = test._append(row)
            # test = test.concat(row)
            # test = pd.concat([test, pd.DataFrame(row.to_dict(), index=[0])], axis=0)
    test.reset_index(drop=True, inplace=True)
    print()
    print(test.dtypes)
    test_stream_id = test["stream_id"]
    test = normal_data.loc[normal_data["stream_id"].isin(test_stream_id)]
    print(test)
    print(test.dtypes)
    ################################# 将所选的DataFrame，通过数据预处理通道，方便模型预测
    test = data_td(test, unique_categories)
    ################################# 根据 modelId 执行对应的模型
    connection = pymysql.connect(host=new_config["host"], port=new_config["port"],
                                 user=new_config["user"], password=new_config["password"], db=new_config["database"])
    cursor = connection.cursor()
    sql = f"SELECT id FROM models WHERE number = {modelId}"
    cursor.execute(sql)
    result_id = cursor.fetchone()
    if result_id is not None:
        id = result_id[0]
    print('id号')
    print(id)
    if id == 1:
        print("%%%%运行model_xgb%%%%")
        connection = pymysql.connect(host=new_config["host"], port=new_config["port"],
                                     user=new_config["user"], password=new_config["password"],
                                     db=new_config["database"])
        cursor = connection.cursor()
        sql = "SELECT training_parameters FROM models WHERE id = 1 ORDER BY time DESC LIMIT 1"
        cursor.execute(sql)
        result = cursor.fetchone()
        print("结果!!!!!!!!!!!!!!!!!!!")
        print(result)
        if result is not None:
            training_parameters = result[0]
            print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!模型装载!!!!!!!!!!!")
            print(training_parameters)
            model_load = joblib.load(training_parameters)
        test_p = test.copy()
        print('打印第一次的test_p.dtypes预测的数据类型！！！！！！！！！！！！！！！！！！！！！')
        print(test_p['stream_id'])
        print(test_p.dtypes)
        # test_p['stream_id']=test_p['stream_id'].astype(str).unique()[0]
        test_p['stream_id'] = int(test_p['stream_id'].unique()[0])
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
        print(test_p['stream_id'])
        # test_p = test_p.drop(['stream_id'], axis=1)
        print('打印test_p.dtypes预测的数据类型！！！！！！！！！！！！！！！！！！！！！！！！！')
        print(test_p.dtypes)
        dtest = xgb.DMatrix(test_p.drop(['异常'], axis=1))
        predict = model_load.predict(dtest)
        predict = pd.DataFrame(predict, columns=['prob'])
        print('predict')
        print(predict)
        test = test.reset_index()
        result = pd.concat([test, predict], axis=1)
        print('result')
        print(result)
        result['异常'] = result.apply(lambda x: 1 if x['prob'] > 0.5 else 0, axis=1)
        result['prob'] = result['异常']
        # 关闭数据库连接
        cursor.close()
        connection.close()
        results = result.drop(['异常', '是否新模拟'], axis=1)
    else:
        print("%%%%运行model23456%%%%")
        print(modelId)
        connection = pymysql.connect(host=new_config["host"], port=new_config["port"],
                                     user=new_config["user"], password=new_config["password"],
                                     db=new_config["database"])
        cursor = connection.cursor()
        sql = f"SELECT training_parameters FROM models WHERE number = {modelId}"
        cursor.execute(sql)
        result = cursor.fetchone()
        if result is not None:
            training_parameters = result[0]
            print(result)
            print(result[0])
            model_load = joblib.load(training_parameters)
        test_p = test.copy()
        test_p = test_p.drop(['stream_id'], axis=1)
        print('打印test_p.dtypes预测的数据类型')
        print(test_p.dtypes)
        x_test = test_p.drop(['异常'], axis=1)
        print(x_test)
        y_pred = model_load.predict(x_test)
        y_pred = pd.DataFrame(y_pred, columns=['prob'])
        print('y_pred')
        print(y_pred)
        test = test.reset_index()
        result = pd.concat([test, y_pred], axis=1)
        print('result')
        print(result)
        # 关闭数据库连接
        cursor.close()
        connection.close()
        results = result.drop(['异常', '是否新模拟'], axis=1)
    results['stream_is_normal'] = results['prob']
    results = results.sort_values(by='stream_id', ascending=True)
    results['stream_is_normal'] = [1 if x == 0 else 0 if x == 1 else x for x in results['stream_is_normal']]
    ################################# 上传结果
    # engine = create_engine('mysql+pymysql://root:123456@localhost/localstreamdata')
    engine = create_engine('mysql+pymysql://' + new_config["user"] + ':' + new_config["password"]
                           + '@' + new_config["host"] + ':' + str(new_config["port"]) + '/' + new_config["database"])
    with engine.connect() as connection:
        conn = connection.connect()
        # 遍历 results['stream_id','stream_is_normal'] 的每一行数据，并更新 normal_data 表的相应行的 stream_is_normal 字段
        for index, row in results[['stream_id', 'stream_is_normal']].iterrows():
            stream_id = row['stream_id']
            stream_is_normal = row['stream_is_normal']
            sql_update = f"UPDATE stream_data SET stream_is_normal = {stream_is_normal} WHERE stream_id = {stream_id}"
            conn.execute(sql_update)
    conn.close()
    counts = results['stream_is_normal'].value_counts()
    if len(counts) > 1:
        ratio_0 = counts[0] / len(results['stream_is_normal'])
        ratio_1 = counts[1] / len(results['stream_is_normal'])
        print("0的比例:", ratio_0)
        print("1的比例:", ratio_1)
    else:
        print("0的比例:100%")
    # return render_template('系统_运行.html')
    return {
        "code": 1,
        "msg": "",
        "data": ""
    }


''''
@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return 'No file part'
    file = request.files['file']
    # 判断是否选择了文件
    if file.filename == '':
        return 'No selected file'
    # 使用 secure_filename 函数来确保文件名安全
    filename, ext = os.path.splitext(secure_filename(file.filename))
    # 在文件名和扩展名之间加上时间戳
    timestamp_filename = filename + '_' + str(int(time.time())) + ext
    # 保存文件到指定位置
    file.save(timestamp_filename)
    print("filename:", timestamp_filename)
    return timestamp_filename
'''


@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return 'No file part'
    file = request.files['file']
    # 判断是否选择了文件
    if file.filename == '':
        return 'No selected file'
    # 使用 secure_filename 函数来确保文件名安全
    print(file.filename)
    filename, ext = os.path.splitext(file.filename)
    filename = secure_filename(filename)
    if filename == "":  # 如果filename为空，则用随机数作为文件名
        filename = str(random.randint(10000, 99999))
    # print(filename)
    # 在文件名和扩展名之间加上时间戳
    timestamp_filename = filename + '_' + str(int(time.time())) + ext
    # 保存文件到指定位置
    file.save(timestamp_filename)
    return timestamp_filename


'''
添加了几个路由：
/download:下载数据集文件
/preview:浏览数据集文件
/remove:删除数据集文件
/preprocess:预处理数据集
/downloadModelParams：下载模型参数
/model/testBatch：使用一批模型进行预测评估
'''


# 下载数据集
@app.route('/download', methods=['GET'])
def download_file():
    # 获取请求中的 filename 参数
    filename = request.args.get('filename')
    # 检查 filename 是否为空或非法字符等
    if not filename:
        return "Filename is required", 400
    # 使用 send_from_directory 发送文件
    try:
        return send_from_directory("./", filename, as_attachment=True)
    except FileNotFoundError:
        return "File not found", 404


# 预览数据集
@app.route('/preview', methods=['GET'])
def preview():
    try:
        filename = request.args.get("filename")
        if filename is None:
            return jsonify({
                "code": 0,
                "data": "",
                "msg": "请求参数不匹配"
            })
        # print("filename:", filename)
        if filename.endswith(".xlsx") or filename.endswith(".xls"):
            df = pd.read_excel(filename)
        elif filename.endswith(".csv"):
            df = pd.read_csv(filename)
        else:
            return jsonify({
                "code": 0,
                "data": "",
                "msg": "文件格式不支持"
            })
        df = df.astype("str")
        columns = df.columns.values.tolist()
        preview_df = df.iloc[:min(df.shape[1], 5), :]  # 最多只预览前五行
        result = [dict(zip(columns, series.values)) for _, series in preview_df.iterrows()]
        return jsonify({
            "code": 1,
            "data": {
                "columns": columns,
                "values": result,
            },
            "msg": ""
        })
    except Exception as ex:
        print(ex)
        return jsonify({
            "code": 0,
            "data": "",
            "msg": "预览失败"
        })


@app.route('/remove', methods=['GET'])
def remove_file():
    # 获取请求中的 filename 参数
    filename = request.args.get('filename')
    # 检查 filename 是否为空或非法字符等
    if not filename:
        return jsonify({
            "code": 0,
            "msg": "文件名不能为空",
            "data": ""
        })
    # 删除文件
    if os.path.exists(filename) and os.path.isfile(filename):
        os.remove(filename)
        return jsonify({
            "code": 1,
            "msg": "",
            "data": "删除成功"
        })
    else:
        return jsonify({
            "code": 0,
            "msg": "文件不存在",
            "data": ""
        })


@app.route('/preprocess', methods=['GET'])
def preprocess():
    preprocess_info = request.args.to_dict()  # 获取前端传来的参数
    print("test!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
    print(preprocess_info)
    # 检查数据集是否已经预处理过了
    datasetId = request.args.get("id")
    connection = pymysql.connect(host=new_config["host"], port=new_config["port"],
                                 user=new_config["user"], password=new_config["password"], db=new_config["database"])
    cursor = connection.cursor()
    sql = "SELECT status FROM dataset WHERE id = %s;"
    cursor.execute(sql, datasetId)
    result = cursor.fetchone()
    cursor.close()
    connection.close()
    if result is None:
        return jsonify({
            "code": 0,
            "data": "",
            "msg": "数据集不存在，请重试"
        })
    if (result[0] == "1"):  # 已经预处理过了
        return jsonify({
            "code": 0,
            "data": "",
            "msg": "该数据集已经预处理完成"
        })
    # 调用数据处理函数,返回处理后的DataFrame
    processed_df = process_data(preprocess_info)
    stats = os.stat(preprocess_info.get('filename'))
    # print(stats.st_size)# 文件大小
    # 更新数据集大小，设置数据集状态为"1"（预处理完成）
    connection = pymysql.connect(host=new_config["host"], port=new_config["port"],
                                 user=new_config["user"], password=new_config["password"], db=new_config["database"])
    cursor = connection.cursor()
    sql = "UPDATE dataset set size=%s,status='1' WHERE id = %s;"
    cursor.execute(sql, (stats.st_size, datasetId))
    connection.commit()
    cursor.close()
    connection.close()
    # 返回处理后的数据集预览
    processed_df = processed_df.astype("str")
    columns = processed_df.columns.values.tolist()
    preview_df = processed_df.iloc[:min(processed_df.shape[1], 5), :]  # 最多只预览前五行
    result = [dict(zip(columns, series.values)) for _, series in preview_df.iterrows()]
    return jsonify({
        "code": 1,
        "data": {
            "columns": columns,
            "values": result,
        },
        "msg": ""
    })


# 预处理实现
def process_data(preprocess_info):
    # 获取请求中的 filename 参数
    filename = preprocess_info.get('filename')
    # 检查 filename 是否为空或非法字符等
    if not filename:
        return jsonify({
            "code": 0,
            "msg": "文件名不能为空",
            "data": ""
        })
    if filename.endswith(".xlsx") or filename.endswith(".xls"):
        df = pd.read_excel(filename)
    elif filename.endswith(".csv"):
        df = pd.read_csv(filename)
    else:
        return jsonify({
            "code": 0,
            "data": "",
            "msg": "文件格式不支持"
        })
    # 数据补齐
    if preprocess_info.get('dataFill') != 'false':
        # 这里选择是k邻近补齐
        imputer = KNNImputer(n_neighbors=5)
        df = pd.DataFrame(imputer.fit_transform(df), columns=df.columns)

    # 归一化和标准化
    if preprocess_info.get('dataNormalize') != 'false':
        columns_to_standardize = ['stream_money', 'stream_consume_type', 'stream_seconds']
        if preprocess_info['dataNormalize'] == 'StandardScaler':
            scaler = StandardScaler()
        elif preprocess_info['dataNormalize'] == 'MinMaxScaler':
            scaler = MinMaxScaler()
        else:
            scaler = None

        if scaler:
            df[columns_to_standardize] = scaler.fit_transform(df[columns_to_standardize])

    # 初始化处理后的数据框为原始数据框
    df_processed = df.copy()

    # 不平衡数据处理
    if preprocess_info.get('balanceProcess') != 'false':
        columns_to_check = ['异常', '是否新模拟']
        for column in columns_to_check:
            X = df.drop(columns=[column])  # 特征
            y = df[column]  # 标签

            # 使用SMOTE进行过采样
            sm = SMOTE(random_state=42)
            X_res, y_res = sm.fit_resample(X, y)

            # 合并重新采样的数据
            df_resampled = pd.concat(
                [pd.DataFrame(X_res, columns=X.columns), pd.DataFrame(y_res, columns=[column])], axis=1)

            # 更新处理后的数据框
            # df_processed = df_resampled
            # 将重新采样后的列更新到处理后的数据框
            df_processed = pd.concat([df_processed.drop(columns=[column]), df_resampled], axis=1)

    # 覆盖原文件
    if filename.endswith(".xlsx") or filename.endswith(".xls"):
        df_processed.to_excel(filename, index=False)
    elif filename.endswith(".csv"):
        df_processed.to_csv(filename, index=False)
    return df_processed


# 下载模型参数
@app.route('/downloadModelParams', methods=['GET'])
def download_model_params():
    # 获取请求中的 filename 参数
    id = request.args.get('id')
    # 检查 filename 是否为空或非法字符等
    if not id:
        return "model id is required", 400
    # 查询模型的参数文件
    connection = pymysql.connect(host=new_config["host"], port=new_config["port"],
                                 user=new_config["user"], password=new_config["password"], db=new_config["database"])
    cursor = connection.cursor()
    print(id)
    sql = "select training_parameters from models where id=%s;"
    cursor.execute(sql, id)
    result = cursor.fetchone()
    cursor.close()
    connection.close()
    if result is None:
        return jsonify({
            "code": 0,
            "data": "",
            "msg": "模型不存在，请重试"
        })
    # 使用 send_from_directory 发送文件
    try:
        return send_from_directory("./", str(result[0], encoding="utf-8"), as_attachment=True)
    except FileNotFoundError:
        return "File not found", 404


@app.route("/model/testBatch", methods=["POST"])
def model_test_batch():
    print(request.json)
    dataIds = request.json.get("dataIds")
    modelIds = request.json.get("modelIds")
    data_ids = [int(id) for id in dataIds.split(",")]
    model_ids = [int(id) for id in modelIds.split(",")]
    ################################# 在MySQL中读取指定行转为DataFrame
    # cnx = mysql.connector.connect(user='ubuntu', password='1', host='************', database='localstreamdata')
    cnx = mysql.connector.connect(user=new_config["user"], password=new_config["password"], host=new_config["host"],
                                  port=new_config["port"], database=new_config["database"])
    cursor = cnx.cursor()
    query = "SELECT * FROM stream_data"
    cursor.execute(query)
    rows = cursor.fetchall()
    column_names = [desc[0] for desc in cursor.description]
    normal_data = pd.DataFrame(rows, columns=column_names)
    cursor.close()
    cnx.close()
    # normal_data["stream_id"] = normal_data["stream_id"].astype(str)
    test = normal_data[normal_data["stream_id"].apply(lambda x: int(x) in data_ids)]
    # print(normal_data["stream_id"].apply(lambda x: str(x) in data_ids))
    # print(test)
    test = pd.DataFrame(columns=normal_data.columns)  # 创建一个空的DataFrame，用于存储符合条件的行
    a = normal_data["stream_id"]
    for index, row in normal_data.iterrows():
        if int(row["stream_id"]) in data_ids:
            test = test._append(row)
            # test = test.concat(row)
            # test = pd.concat([test, pd.DataFrame(row.to_dict(), index=[0])], axis=0)
    test.reset_index(drop=True, inplace=True)
    print()
    print(test.dtypes)
    test_stream_id = test["stream_id"]
    test = normal_data.loc[normal_data["stream_id"].isin(test_stream_id)]
    print(test)
    print(test.dtypes)
    ################################# 将所选的DataFrame，通过数据预处理通道，方便模型预测
    test = data_td(test, unique_categories)
    ################################# 根据 modelId 执行对应的模型
    result_list = []
    for modelId in model_ids:
        print("modelId", modelId)
        connection = pymysql.connect(host=new_config["host"], port=new_config["port"],
                                     user=new_config["user"], password=new_config["password"],
                                     db=new_config["database"])
        cursor = connection.cursor()
        sql = f"SELECT id FROM models WHERE number = {modelId}"
        cursor.execute(sql)
        result_id = cursor.fetchone()
        if result_id is not None:
            id = result_id[0]
        print('id号')
        print(id)
        cursor.close()
        connection.close()
        if id == 1:
            print("%%%%运行model_xgb%%%%")
            connection = pymysql.connect(host=new_config["host"], port=new_config["port"],
                                         user=new_config["user"], password=new_config["password"],
                                         db=new_config["database"])
            cursor = connection.cursor()
            sql = "SELECT training_parameters FROM models WHERE id = 1 ORDER BY time DESC LIMIT 1"
            cursor.execute(sql)
            result = cursor.fetchone()
            print("结果!!!!!!!!!!!!!!!!!!!")
            print(result)
            if result is not None:
                training_parameters = result[0]
                print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!模型装载!!!!!!!!!!!")
                print(training_parameters)
                model_load = joblib.load(training_parameters)
            test_p = test.copy()
            print('打印第一次的test_p.dtypes预测的数据类型！！！！！！！！！！！！！！！！！！！！！')
            print(test_p['stream_id'])
            print(test_p.dtypes)
            # test_p['stream_id']=test_p['stream_id'].astype(str).unique()[0]
            test_p['stream_id'] = int(test_p['stream_id'].unique()[0])
            print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
            print(test_p['stream_id'])
            # test_p = test_p.drop(['stream_id'], axis=1)
            print('打印test_p.dtypes预测的数据类型！！！！！！！！！！！！！！！！！！！！！！！！！')
            print(test_p.dtypes)
            dtest = xgb.DMatrix(test_p.drop(['异常'], axis=1))
            predict = model_load.predict(dtest)
            predict = pd.DataFrame(predict, columns=['prob'])
            print('predict')
            print(predict)
            test = test.reset_index()
            result = pd.concat([test, predict], axis=1)
            print('result')
            print(result)
            result['异常'] = result.apply(lambda x: 1 if x['prob'] > 0.5 else 0, axis=1)
            result['prob'] = result['异常']
            # 关闭数据库连接
            cursor.close()
            connection.close()
            results = result.drop(['异常', '是否新模拟'], axis=1)
        else:
            print("%%%%运行model23456%%%%")
            print(modelId)
            connection = pymysql.connect(host=new_config["host"], port=new_config["port"],
                                         user=new_config["user"], password=new_config["password"],
                                         db=new_config["database"])
            cursor = connection.cursor()
            sql = f"SELECT training_parameters FROM models WHERE number = {modelId}"
            cursor.execute(sql)
            result = cursor.fetchone()
            if result is not None:
                training_parameters = result[0]
                print(result)
                print(result[0])
                model_load = joblib.load(training_parameters)
            test_p = test.copy()
            test_p = test_p.drop(['stream_id'], axis=1)
            print('打印test_p.dtypes预测的数据类型')
            print(test_p.dtypes)
            x_test = test_p.drop(['异常'], axis=1)
            print(x_test)
            y_pred = model_load.predict(x_test)
            y_pred = pd.DataFrame(y_pred, columns=['prob'])
            print('y_pred')
            print(y_pred)
            test_p = test.copy().reset_index()
            result = pd.concat([test_p, y_pred], axis=1)
            print('result')
            print(result)
            # 关闭数据库连接
            cursor.close()
            connection.close()
            results = result.drop(['异常', '是否新模拟'], axis=1)
        results['stream_is_normal'] = results['prob']
        results = results.sort_values(by='stream_id', ascending=True)
        results['stream_is_normal'] = [1 if x == 0 else 0 if x == 1 else x for x in results['stream_is_normal']]
        result_list.append(results['stream_is_normal'].values.tolist())
    return jsonify({
        "code": 1,
        "msg": "",
        "data": result_list
    })


if __name__ == '__main__':
    # MySQL读取数据

    cnx = mysql.connector.connect(user=new_config["user"], password=new_config["password"], host=new_config["host"],
                                  port=new_config["port"], database=new_config["database"])
    # cnx = mysql.connector.connect(user='ubuntu', password='1', host='************', database='localstreamdata')
    cursor = cnx.cursor()
    # 执行查询 normal_data
    query = "SELECT * FROM stream_data"
    cursor.execute(query)
    rows = cursor.fetchall()
    column_names = [desc[0] for desc in cursor.description]  # 获取列名
    normal_data = pd.DataFrame(rows, columns=column_names)
    # 执行查询 train_data_process
    query = "SELECT * FROM train_data_process"
    cursor.execute(query)
    rows = cursor.fetchall()
    column_names = [desc[0] for desc in cursor.description]  # 获取列名
    train_data_process = pd.DataFrame(rows, columns=column_names)
    # 关闭游标和数据库连接
    cursor.close()
    cnx.close()

    unique_categories = train_data_process['stream_consume_location'].unique()
    unique_categories2 = normal_data['stream_consume_location'].unique()
    # print(unique_categories)
    # print(unique_categories2)
    # print(len(unique_categories))
    # print(len(unique_categories2))
    unique_categories = np.unique(np.concatenate((unique_categories, unique_categories2), axis=None))
    # print(len(unique_categories))
    app.run(host='localhost', port=338088)

package com.lcc.localStreamData;

import com.lcc.localStreamData.client.BigDataClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

@Slf4j
@SpringBootApplication
@EnableScheduling
@ServletComponentScan
@EnableFeignClients(clients = {BigDataClient.class})
public class LocalStreamDataApplication {
    public static void main(String[] args) {
        SpringApplication.run(LocalStreamDataApplication.class);
    }
}

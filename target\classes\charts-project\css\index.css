/*清除元素默认的内外边距  */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
/*让所有斜体 不倾斜*/
em,
i {
    font-style: normal;
}
/*去掉列表前面的小点*/
li {
    list-style: none;
}
/*图片没有边框   去掉图片底侧的空白缝隙*/
img {
    border: 0; /*ie6*/
    vertical-align: middle;
}
/*让button 按钮 变成小手*/
button {
    cursor: pointer;
}
/*取消链接的下划线*/
a {
    color: #666;
    text-decoration: none;
}

h4 {
    font-weight: 400;
}

a:hover {
    color: #e33333;
}

body {
    background: url(../images/bg.jpg) no-repeat;
    background-size: cover;
}

/* // 实现rem适配 */
@media screen and (max-width: 1024px) {
    html {
        font-size: 42.66px !important;
    }
}

@media screen and (min-width: 1920px) {
    html {
        font-size: 80px !important;
    }
}

.viewport {
    display: flex;
    min-width: 1024px;
    max-width: 1920px;
    margin: 0 auto;
    padding: 1.1rem .25rem 0;
    background: url(../images/logo.png) no-repeat;
    min-height: 780px;
    background-size: contain;
}

.viewport .column {
    flex: 3;
}

.viewport .column:nth-child(2) {
    flex: 4;
    margin: .4rem .25rem 0;
}

/* 公共面板样式  */
.panel {
    position: relative;
    border: 15px solid transparent;
    border-width: 0.6375rem 0.475rem 0.25rem 1.65rem;
    border-image-source: url(../images/border.png);
    border-image-slice: 51 38 20 132;
    margin-bottom: 0.25rem;
}

.panel h3 {
    font-size: 0.25rem;
    color: white;
    font-weight: 400;
}

.inner {
    position: absolute;
    top: -0.6375rem;
    left: -1.65rem;
    right: -0.475rem;
    bottom: -0.25rem;
    padding: 0.3rem 0.45rem;
}

/* 概览区域模块制作 */
.overview {
    height: 1.375rem;
}
.overview ul {
    display: flex;
    justify-content: space-between;
}
.overview ul li h4 {
    font-size: 0.35rem;
    color: #fff;
    margin: 0 0 0.1rem 0.06rem;
}
.overview ul li span {
    font-size: 0.2rem;
    color: #4c9bfd;
}

/* 监控区域模块制作 */
.monitor {
    height: 6rem;
}

.monitor .inner{
    padding: .3rem 0;
    display: flex;
    flex-direction: column;
}
.monitor .tabs{
    padding: 0 .45rem;
    margin-bottom: 0.225rem;
    display: flex;
}
.monitor .tabs a{
    color:#1950c4;
    font-size: 0.225rem;
    padding: 0 .3375rem;
}
.monitor .tabs a:first-child{
    padding-left: 0;
}
.monitor .tabs a.active{
    color: #fff;
}
.monitor .content{
    flex: 1;
    position: relative;
    display: none;
}
.monitor .head{
    display: flex;
    justify-content: space-between;
    line-height: 1.05;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 0.15rem 0.45rem;
    color: #68d8fe;
    font-size: 0.175rem;
}
.monitor .marquee-view {
    position: absolute;
    top: 0.5rem;
    bottom: 0;
    width: 100%;
    overflow: hidden;
}
.monitor .row{
    display: flex;
    justify-content: space-between;
    line-height: 1.05;
    font-size: 0.15rem;
    color: #61a8ff;
    padding: 0.15rem 0.45rem;
}
.monitor .row .icon-dot{
    position: absolute;
    left: 0.2rem;
    opacity: 0;
}
.monitor .row:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #68d8fe;
}
.monitor .row:hover .icon-dot{
    opacity: 1;
}
.monitor .col:first-child{
    width: 1rem;
}
.monitor .col:nth-child(2){
    width: 2.5rem;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.monitor .col:nth-child(3){
    width: 1rem;
}

/* 通过CSS3动画滚动marquee */
.marquee-view .marquee {
    animation: move 15s linear infinite;
}
@keyframes move {
    0% {
    }
    100% {
        transform: translateY(-50%);
    }
}
/* 3.鼠标经过marquee 就停止动画 */
.marquee-view .marquee:hover {
    animation-play-state: paused;
}

/* 点位 */
.point {
    height: 4.25rem;
}
.point .chart {
    display: flex;
    margin-top: 0.3rem;
    justify-content: space-between;
}
.point .pie {
    width: 3.9rem;
    height: 3rem;
    margin-left: -0.125rem;
}
.point .data {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 2.1rem;
    padding: .45rem .375rem;
    box-sizing: border-box;
    background-image: url(../images/rect.png);
    background-size: cover;
}
.point h4 {
    margin-bottom: 0.15rem;
    font-size: .35rem;
    color: #fff;
}
.point span {
    display: block;
    color: #4c9bfd;
    font-size: .2rem;
}

/* 地图  */
.map {
    height: 7.225rem;
    margin-bottom: 0.25rem;
    display: flex;
    flex-direction: column;
}
.map h3 {
    line-height: 1;
    padding: 0.2rem 0;
    margin: 0;
    font-size: 0.25rem;
    color: #fff;
    font-weight: 400;
}
.map .icon-cube {
    color: #68d8fe;
}
.map .chart {
    flex: 1;
    background-color: rgba(255, 255, 255, 0.05);
}
.map .geo {
    width: 100%;
    height: 100%;
}

/* 用户模块 */
.users {
    height: 4.25rem;
    display: flex;
}
.users .chart {
    display: flex;
    margin-top: .3rem;
}
.users .bar {
    width: 7.35rem;
    height: 3rem;
}
.users .data {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 2.1rem;
    padding: .45rem .375rem;
    box-sizing: border-box;
    background-image: url(../images/rect.png);
    background-size: cover;
}
.users h4 {
    margin-bottom: .15rem;
    font-size: .35rem;
    color: #fff;
}
.users span {
    display: block;
    color: #4c9bfd;
    font-size: 0.2rem;
}

/* 订单 */
.order {
    height: 1.875rem;
}
.order .filter {
    display: flex;
}
.order .filter a {
    display: block;
    height: 0.225rem;
    line-height: 1;
    padding: 0 0.225rem;
    color: #1950c4;
    font-size: 0.225rem;
    border-right: 0.025rem solid #00f2f1;
}
.order .filter a:first-child {
    padding-left: 0;
}
.order .filter a:last-child {
    border-right: none;
}
.order .filter a.active {
    color: #fff;
    font-size: 0.25rem;
}
.order .data {
    display: flex;
    margin-top: 0.25rem;
}
.order .item {
    width: 50%;
}
.order h4 {
    font-size: 0.35rem;
    color: #fff;
    margin-bottom: 0.125rem;
}
.order span {
    display: block;
    color: #4c9bfd;
    font-size: 0.2rem;
}

/* 销售区域 */
.sales {
    height: 3.1rem;
}
.sales .caption {
    display: flex;
    line-height: 1;
}
.sales h3 {
    height: 0.225rem;
    padding-right: 0.225rem;
    border-right: 0.025rem solid #00f2f1;
}
.sales a {
    padding: 0.05rem;
    font-size: 0.2rem;
    margin: -0.0375rem 0 0 0.2625rem;
    border-radius: 0.0375rem;
    color: #0bace6;
}
.sales a.active {
    background-color: #4c9bfd;
    color: #fff;
}
.sales .inner {
    display: flex;
    flex-direction: column;
}
.sales .chart {
    flex: 1;
    padding-top: 0.1875rem;
    position: relative;
}
.sales .label {
    position: absolute;
    left: 0.525rem;
    top: 0.225rem;
    color: #4996f5;
    font-size: 0.175rem;
}
.sales .line {
    width: 100%;
    height: 100%;
    /*background-color: pink;*/
}

/* 渠道区块 */
.wrap {
    display: flex;
}
.channel,
.quarter {
    flex: 1;
    height: 2.9rem;
}
.channel {
    margin-right: 0.25rem;
}
.channel .data {
    /*overflow: hidden;*/
}
.channel .data .radar {
    height: 2.1rem;
    width: 100%;
}
.channel h4 {
    color: #fff;
    font-size: 0.4rem;
    margin-bottom: 0.0625rem;
}
.channel small {
    font-size: 50%;
}
.channel span {
    display: block;
    color: #4c9bfd;
    font-size: 0.175rem;
}
/* 季度区块 */
.quarter .inner {
    display: flex;
    flex-direction: column;
    margin: 0 -0.075rem;
}
.quarter .chart {
    flex: 1;
    padding-top: 0.225rem;
}
.quarter .box {
    position: relative;
}
.quarter .label {
    transform: translate(-50%, -30%);
    color: #fff;
    font-size: 0.375rem;
    position: absolute;
    left: 50%;
    top: 50%;
}
.quarter .label small {
    font-size: 50%;
}
.quarter .gauge {
    height: 1.05rem;
}
.quarter .data {
    display: flex;
    justify-content: space-between;
}
.quarter .item {
    width: 50%;
}
.quarter h4 {
    color: #fff;
    font-size: 0.3rem;
    margin-bottom: 0.125rem;
}
.quarter span {
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    color: #4c9bfd;
    font-size: 0.175rem;
}

/* 排行榜 */
.top {
    height: 3.5rem;
}
.top .inner {
    display: flex;
}
.top .all {
    display: flex;
    flex-direction: column;
    width: 2.1rem;
    color: #4c9bfd;
    font-size: 0.175rem;
    vertical-align: middle;
}
.top .all ul {
    padding-left: 0.15rem;
    margin-top: 0.15rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
}
.top .all li {
    overflow: hidden;
}
.top .all [class^="icon-"] {
    font-size: 0.45rem;
    vertical-align: middle;
    margin-right: 0.15rem;
}
.top .province {
    flex: 1;
    display: flex;
    flex-direction: column;
    color: #fff;
}
.top .province i {
    padding: 0 0.15rem;
    margin-top: 0.0625rem;
    float: right;
    font-style: normal;
    font-size: 0.175rem;
    color: #0bace6;
}
.top .province s {
    display: inline-block;
    transform: scale(0.8);
    text-decoration: none;
}
.top .province .icon-up {
    color: #dc3c33;
}
.top .province .icon-down {
    color: #36be90;
}
.top .province .data {
    flex: 1;
    display: flex;
    margin-top: 0.175rem;
}
.top .province ul {
    flex: 1;
    line-height: 1;
    margin-bottom: 0.175rem;
}
.top .province ul li {
    display: flex;
    justify-content: space-between;
}
.top .province ul span {
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.top .province ul.sup {
    font-size: 0.175rem;
}
.top .province ul.sup li {
    color: #4995f4;
    padding: 0.15rem;
}
.top .province ul.sup li.active {
    color: #a3c6f2;
    background-color: rgba(10, 67, 188, 0.2);
}
.top .province ul.sub {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    font-size: 0.15rem;
    background-color: rgba(10, 67, 188, 0.2);
}
.top .province ul.sub li {
    color: #52ffff;
    padding: 0.125rem 0.175rem;
}
.clock {
    position: absolute;
    top: -0.45rem;
    right: 0.5rem;
    font-size: 0.25rem;
    color: #0bace6;
}
.clock i {
    margin-right: 5px;
    font-size: 0.25rem;
}
@media screen and (max-width: 1600px) {
    .top span {
        transform: scale(0.9);
    }
    .top .province ul.sup li {
        padding: 0.125rem 0.15rem;
    }
    .top .province ul.sub li {
        padding: 0.0625rem 0.15rem;
    }
    .quarter span {
        transform: scale(0.9);
    }
}
package com.lcc.localStreamData.controller;

import com.lcc.localStreamData.client.BigDataClient;
import com.lcc.localStreamData.common.R;
import com.lcc.localStreamData.entity.StreamData;
import com.lcc.localStreamData.service.StreamDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
public class RiskController {

    @Autowired
    private StreamDataService streamDataService;
    @Autowired
    BigDataClient bigDataClient;

    @GetMapping("/risk/list")
    public R<List<StreamData>> list() {
        List<StreamData> list = streamDataService.findTodayList();
        return R.success(list);
    }


    @PostMapping("/risk/result")
    public R<List> result(@RequestBody Map param) {
        String dataIds = (String) param.get("dataIds");
        String[] ids = dataIds.split(",");
        List list = streamDataService.getListByIds(ids);
        return R.success(list);
    }

    @PostMapping("/rule/test")
    public R<String> judge(@RequestBody Map param) {
        log.info("test params:{}", param);
        Integer ruleGroupId = (Integer) param.get("modelId");
        String ids = (String) param.get("dataIds");
        String[] dataIds = ids.split(",");

        List<Integer> result = streamDataService.judgeByRuleGroupId(ruleGroupId, dataIds);
        streamDataService.updateJudgeResult(Arrays.asList(dataIds), result);

        return R.success("操作成功");
    }

    @PostMapping("/fusion/test")
    public R<String> ruleJudgeOfFusion(@RequestBody Map param) {
        log.info("test params:{}", param);
        //远程调用python端获取模型评估结果
        R<List<List<Integer>>> ModelJudgeResult = bigDataClient.judgeBatchByModel(param);
        //本地使用规则进行评估
        String ruleGroupIdsStr = (String) param.get("ruleGroupIds");
        String[] ruleGroupIds = ruleGroupIdsStr.split(",");
        String dataIdsStr = (String) param.get("dataIds");
        String[] dataIds = dataIdsStr.split(",");
        List<List<Integer>> resultListOfRule = streamDataService.judgeBatchByRuleGroupIds(ruleGroupIds, dataIds);
        //将模型评估结果和规则评估结果放进同一个数组
        resultListOfRule.addAll(ModelJudgeResult.getData());
        //投票法对评估结果进行融合
        double[] sumArr = new double[resultListOfRule.get(0).size()];
        for (List<Integer> tmp : resultListOfRule) {
            for (int j = 0; j < tmp.size(); j++) {
                sumArr[j] += tmp.get(j);
            }
        }
        List<Integer> result = new ArrayList<>();
        for (double sum : sumArr) {
            if (sum > 0.5 * resultListOfRule.size()) {//半数以上投票
                result.add(1);
            } else {
                result.add(0);
            }
        }
        log.info("融合评估结果:{}", result);
        System.out.println(result);
        //更新结果
        streamDataService.updateJudgeResult(Arrays.asList(dataIds), result);
        return R.success("");
    }


}

package com.lcc.localStreamData.client;

import com.lcc.localStreamData.common.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@FeignClient(name = "big-data", url = "${feign-client.url.big-data}")
public interface BigDataClient {
    @PostMapping(value = "/model/testBatch")
    public R<List<List<Integer>>> judgeBatchByModel(@RequestBody Map params);
}

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 让所有斜体不倾斜 */
em, i {
  font-style: normal;
}

/* 去掉列表前面的小点 */
li {
  list-style: none;
}

/* 图片没有边框，去掉图片底侧的空白缝隙 */
img {
  border: 0;
  vertical-align: middle;
}

/* 让button按钮变成小手 */
button {
  cursor: pointer;
}

/* 取消链接的下划线 */
a {
  color: #666;
  text-decoration: none;
}

a:hover {
  color: #409EFF;
}

h4 {
  font-weight: 400;
}

/* 全局字体设置 */
body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  background-color: #f5f5f5;
}

/* Element Plus 样式覆盖 */
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-button {
  border-radius: 4px;
}

/* 响应式设计 */
@media screen and (max-width: 1024px) {
  html {
    font-size: 14px;
  }
}

@media screen and (min-width: 1920px) {
  html {
    font-size: 16px;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

<template>
  <div class="real-time-analysis-module">
    <el-card>
      <template #header>
        <div class="module-header">
          <h2>金融欺诈实时数据分析</h2>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>金融欺诈实时数据分析</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </template>

      <!-- 子模块导航 -->
      <div class="sub-navigation">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="流数据预处理" name="preprocessing">
            <template #label>
              <span class="tab-label">
                <el-icon><Filter /></el-icon>
                流数据预处理
              </span>
            </template>
          </el-tab-pane>
          <el-tab-pane label="风险评估规则/决策训练" name="rule-training">
            <template #label>
              <span class="tab-label">
                <el-icon><Tools /></el-icon>
                风险评估规则/决策训练
              </span>
            </template>
          </el-tab-pane>
          <el-tab-pane label="模型发布与决策训练" name="model-deployment">
            <template #label>
              <span class="tab-label">
                <el-icon><Upload /></el-icon>
                模型发布与决策训练
              </span>
            </template>
          </el-tab-pane>
          <el-tab-pane label="风险实时评估与预警" name="assessment">
            <template #label>
              <span class="tab-label">
                <el-icon><Warning /></el-icon>
                风险实时评估与预警
              </span>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 子页面内容 -->
      <div class="module-content">
        <router-view />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Filter, Tools, Upload, Warning } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const activeTab = ref('preprocessing')

// 根据当前路由设置活跃标签
const setActiveTabFromRoute = () => {
  const routeName = route.name
  switch (routeName) {
    case 'StreamPreprocessing':
      activeTab.value = 'preprocessing'
      break
    case 'RiskRuleTraining':
      activeTab.value = 'rule-training'
      break
    case 'ModelDeployment':
      activeTab.value = 'model-deployment'
      break
    case 'RealTimeAssessment':
      activeTab.value = 'assessment'
      break
    default:
      activeTab.value = 'preprocessing'
  }
}

// 当前页面标题
const currentPageTitle = computed(() => {
  switch (activeTab.value) {
    case 'preprocessing':
      return '流数据预处理'
    case 'rule-training':
      return '风险评估规则/决策训练'
    case 'model-deployment':
      return '模型发布与决策训练'
    case 'assessment':
      return '风险实时评估与预警'
    default:
      return '流数据预处理'
  }
})

// 标签切换处理
const handleTabChange = (tabName) => {
  switch (tabName) {
    case 'preprocessing':
      router.push('/real-time-analysis/preprocessing')
      break
    case 'rule-training':
      router.push('/real-time-analysis/rule-training')
      break
    case 'model-deployment':
      router.push('/real-time-analysis/model-deployment')
      break
    case 'assessment':
      router.push('/real-time-analysis/assessment')
      break
  }
}

// 监听路由变化
watch(() => route.name, () => {
  setActiveTabFromRoute()
}, { immediate: true })
</script>

<style scoped>
.real-time-analysis-module {
  height: 100%;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.module-header h2 {
  margin: 0;
  color: #333;
}

.sub-navigation {
  margin-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.module-content {
  min-height: 500px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-tabs__header) {
  margin: 0 0 20px 0;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__item) {
  padding: 0 20px;
  height: 50px;
  line-height: 50px;
  font-size: 14px;
}

:deep(.el-tabs__item.is-active) {
  color: #409EFF;
  font-weight: 600;
}
</style>

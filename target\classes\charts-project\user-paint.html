<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/elementui.css">
    <script src="js/echarts.min.js"></script>
    <script src="js/jquery.min.js"></script>
    <script src="js/china.js"></script>
    <style>
        #app {
            padding: 20px;
        }
        .page {
            margin-top: 10px;
            text-align: center;
        }
        .header {

        }
        .header .search {
            width: 230px;
        }
        .table {
            border: 2px solid #f3f4f7;
            margin-top: 20px;

        }

        .left {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 3px 3px 2px #e7e7e7;
            padding-top: 20px;
            padding-bottom: 20px;
            height: 685px;
        }

        .right {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 3px 3px 2px #e7e7e7;
            padding-top: 20px;
            padding-bottom: 20px;
            height: 685px;
        }

        .bread {
            margin-bottom: 20px;
            background-color: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 3px 3px 2px #e7e7e7;
        }

        .right .title {
            font-size: 25px;
            font-weight: bold;
            border-bottom: 1px solid #303133;
            padding-bottom: 20px;
            color: #303133;
            text-align: center;
            height: 30px;
        }


        .switch {
            margin-top: 8px;
        }

        #chart1 {
            width: 100%;
            height: 600px;
        }
        #chart2 {
            width: 100%;
            height: 600px;
        }

        /* 监控区域模块制作 */
        .monitor {
            height: 6rem;
        }

        .monitor .inner{
            padding: .3rem 0;
            display: flex;
            flex-direction: column;
        }
        .monitor .tabs{
            padding: 0 .45rem;
            margin-bottom: 0.225rem;
            display: flex;
        }
        .monitor .tabs a{
            color:#1950c4;
            font-size: 0.225rem;
            padding: 0 .3375rem;
        }
        .monitor .tabs a:first-child{
            padding-left: 0;
        }
        .monitor .tabs a.active{
            color: #fff;
        }
        .monitor .content{
            flex: 1;
            position: relative;
            display: none;
        }
        .monitor .head{
            display: flex;
            justify-content: space-between;
            line-height: 1.05;
            background-color: rgba(255, 255, 255, 0.1);
            padding: 0.15rem 0.45rem;
            color: #68d8fe;
            font-size: 0.175rem;
        }
        .monitor .marquee-view {
            position: absolute;
            top: 0.5rem;
            bottom: 0;
            width: 100%;
            overflow: hidden;
        }
        .monitor .row{
            display: flex;
            justify-content: space-between;
            line-height: 1.05;
            font-size: 0.15rem;
            color: #61a8ff;
            padding: 0.15rem 0.45rem;
        }
        .monitor .row .icon-dot{
            position: absolute;
            left: 0.2rem;
            opacity: 0;
        }
        .monitor .row:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #68d8fe;
        }
        .monitor .row:hover .icon-dot{
            opacity: 1;
        }
        .monitor .col:first-child{
            width: 1rem;
        }
        .monitor .col:nth-child(2){
            width: 2.5rem;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
        .monitor .col:nth-child(3){
            width: 1rem;
        }

        /* 通过CSS3动画滚动marquee */
        .marquee-view .marquee {
            animation: move 15s linear infinite;
        }
        @keyframes move {
            0% {
            }
            100% {
                transform: translateY(-50%);
            }
        }
        /* 3.鼠标经过marquee 就停止动画 */
        .marquee-view .marquee:hover {
            animation-play-state: paused;
        }
    </style>
</head>
<body>
<div id="app">

    <div class="bread">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item><a href="/">首页</a></el-breadcrumb-item>
            <el-breadcrumb-item><a href="/">用户预警</a></el-breadcrumb-item>
        </el-breadcrumb>
    </div>

    <el-row :gutter="20">
        <el-col :span="8">
            <div class="left">
                <div class="table">
                    <el-table
                            :data="tableData"
                            stripe
                            style="width: 100%">
                        <el-table-column
                                type="index"
                                width="80">
                        </el-table-column>
                        <el-table-column
                                prop="streamUserId"
                                label="用户ID"
                                width="150">
                        </el-table-column>
                        <el-table-column
                                prop="streamSignLocation"
                                label="注册地"
                                width="150">
                        </el-table-column>
                        <el-table-column label="操作">
                            <template slot-scope="scope">
                                <el-button type="primary" icon="el-icon-search" plain>用户分析</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="page">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[100, 200, 300, 400]"
                            :page-size="100"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="400">
                    </el-pagination>
                </div>
            </div>
        </el-col>
        <el-col :span="16">
            <div class="right">
                <div class="title">用户分析结果</div>
                <div style="background-image:url(images/bg.jpg);height: ">
                    <el-row :gutter="20">
                        <el-col :span="13">
                            <div id="chart1"></div>
                        </el-col>
                        <el-col :span="11">
                            <div style="padding-top: 15px;padding-bottom: 15px;padding-left: 15px">
                                <div>
                                    <el-row :gutter="20">
                                        <el-col :span="12">
                                            <dv-border-box-1>
                                                <dv-active-ring-chart :config="pieConfig" style="width:300px;height:300px;margin-left: -35px" />
                                            </dv-border-box-1>
                                        </el-col>
                                        <el-col :span="12">
                                            <dv-border-box-1>
                                                <dv-charts :option="normalOption" style="width:300px;height:300px;margin-left: -32px"/>
                                            </dv-border-box-1>
                                        </el-col>
                                    </el-row>

                                </div>
                                <div style="height:300px;margin-top: 20px;">
                                    <dv-border-box-1>
                                        <div style="height: 25px"></div>
                                        <div style="margin-left: 50px;">
                                            <dv-capsule-chart :config="listConfig" style="width:86%;height:250px" />
                                        </div>
                                    </dv-border-box-1>
                                </div>
                            </div>

                        </el-col>
                    </el-row>
                </div>
            </div>
        </el-col>
    </el-row>





</div>
</body>
<script src="js/vue.js"></script>
<script src="js/axios.js"></script>
<script src="js/elementui.js"></script>
<!--调试版-->
<script src="https://unpkg.com/@jiaminghi/data-view/dist/datav.map.vue.js"></script>
<script>
    new Vue({
        el: "#app",
        data() {
            return {
                tableData: [{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 1
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                },{
                    streamTime: '2016-05-02',
                    streamUserId: '60591',
                    streamConsumeLocation: '广东省-广州市',
                    streamSignLocation: '江苏省',
                    streamIsNormal: 0
                }],
                currentPage: 1,
                condition: "",
                opt: "",
                pieConfig: {
                    data: [
                        {
                            name: '周口',
                            value: 55
                        },
                        {
                            name: '南阳',
                            value: 120
                        },
                        {
                            name: '西峡',
                            value: 78
                        },
                        {
                            name: '驻马店',
                            value: 66
                        },
                        {
                            name: '新乡',
                            value: 80
                        }
                    ]
                },
                listConfig: {
                    data: [
                        {
                            name: '南阳',
                            value: 167
                        },
                        {
                            name: '周口',
                            value: 67
                        },
                        {
                            name: '漯河',
                            value: 123
                        },
                        {
                            name: '郑州',
                            value: 55
                        },
                        {
                            name: '西峡',
                            value: 98
                        }
                    ]
                },
                normalOption: {
                    title: {
                        text: '异常消费占比',
                        style: {
                            fill: '#fff'
                        }
                    },
                    series: [
                        {
                            type: 'gauge',
                            data: [ { name: 'itemA', value: 55 } ],
                            center: ['50%', '55%'],
                            axisLabel: {
                                formatter: '{value}%',
                                style: {
                                    fill: '#fff'
                                }
                            },
                            axisTick: {
                                style: {
                                    stroke: '#fff'
                                }
                            },
                            animationCurve: 'easeInOutBack'
                        }
                    ]
                }
            }
        },
        methods: {
            handleSizeChange(val) {
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                console.log(`当前页: ${val}`);
            },
            goBack() {
                console.log('go back');
            }
        }
    });
</script>
<script>
    // 动画
    $(".marquee-view .marquee").each(function() {
        // console.log($(this));
        var rows = $(this).children().clone();
        $(this).append(rows);
    });

    var chart1 = echarts.init(document.getElementById("chart1"));
    var data = [
                    {
                        name: "海门",
                        value: 12
                    },
                    {
                        name: "鄂尔多斯",
                        value: 30
                    }
                ]

    var geoCoordMap = {
        "海门": [121.15, 31.89],
        "鄂尔多斯": [109.781327, 39.608266]
    };

    function convertData(data) {
        var res = [];
        for (var i = 0; i < data.length; i++) {
            var geoCoord = geoCoordMap[data[i].name];
            if (geoCoord) {
                res.push({
                    name: data[i].name,
                    value: geoCoord.concat(data[i].value)
                });
            }
        }
        return res;
    }
    var option = {
        geo: {
            show: true,
            map: 'china',
            label: {
                emphasis: {
                    show: true,
                    color: '#fff'
                }
            },
            roam: false,
            itemStyle: {
                normal: {
                    areaColor: '#01215c',
                    borderWidth: 1,//设置外层边框
                    borderColor:'#9ffcff',
                    shadowColor: 'rgba(0,54,255, 1)',
                    shadowBlur: 30
                },
                emphasis:{
                    areaColor: '#01215c',
                }
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                return params.name + ' : ' + (params.value+"").split(",")[2];
            }
        },
        series: [
            {
                type: 'map',
                map: 'china',
                geoIndex: 1,
                aspectScale: 0.75, //长宽比
                showLegendSymbol: false, // 存在legend时显示
                tooltip:{
                    show:false,
                },
                label: {
                    normal: {
                        show: false
                    },
                    emphasis: {
                        show: false
                    }
                } ,
                roam: false,

                itemStyle: {
                    normal: {
                        areaColor: '#01215c',
                        borderColor: '#3074d0',
                        borderWidth: 1
                    },
                    emphasis: {
                        areaColor: '#01215c'
                    }
                },
            },
            {
                name: '散点',
                type: 'effectScatter',
                coordinateSystem: 'geo',
                data: convertData(data),
                symbolSize: 15,//调整散点大小
                symbol: 'circle',
                label: {
                    normal: {
                        show: true, //城市名称
                        color: "#fff",
                        width: 1,
                        opacity: 0.6,
                        offset: [10, -20],
                        formatter: '{b}'
                    },
                    emphasis: {
                        show: false
                    }
                },
                showEffectOn: 'render',
                itemStyle: {
                    normal: {
                        color: {
                            type: 'radial',
                            x: 0.5,
                            y: 0.5,
                            r: 0.5,
                            colorStops: [{
                                offset: 0,
                                color: 'rgba(14,245,209,0.2)'
                            }, {
                                offset: 0.8,
                                color: 'rgba(14,245,209,0.2)'
                            }, {
                                offset: 1,
                                color: 'rgba(14,245,209,1)'
                            }],
                            global: false // 缺省为 false
                        },
                    }

                },

            },
        ]
    }
    chart1.setOption(option);

    var chart2 = echarts.init(document.getElementById("chart2"));
    var chart2_option = {
        title: {
            text: '用户消费分布',
            left: 'center'
        },
        tooltip: {
            trigger: 'item'
        },
        series: [
            {
                name: 'Access From',
                type: 'pie',
                radius: '50%',
                data: [
                    { value: 1048, name: 'Search Engine' },
                    { value: 735, name: 'Direct' },
                    { value: 580, name: 'Email' },
                    { value: 484, name: 'Union Ads' },
                    { value: 300, name: 'Video Ads' }
                ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    };
    chart2.setOption(chart2_option);


    window.addEventListener("resize", function () {
        chart1.resize();
        chart2.resize();
    })
</script>
</html>
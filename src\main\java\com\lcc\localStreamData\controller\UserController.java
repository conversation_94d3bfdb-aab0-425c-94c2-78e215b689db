package com.lcc.localStreamData.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcc.localStreamData.common.R;
import com.lcc.localStreamData.entity.User;
import com.lcc.localStreamData.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

@Slf4j
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;


    @GetMapping("/list")
    public R<Page<User>> list(int page, int pageSize,String username){
        Page<User> list = userService.findList(page,pageSize,username);
        return R.success(list);
    }

    @GetMapping("/delete")
    public R<String> delete(int id){
        userService.removeById(id);
        return R.success("操作成功");
    }

    @GetMapping("/active")
    public R<String> active(int id){
        userService.active(id);
        return R.success("操作成功");
    }

    @GetMapping("/lock")
    public R<String> lock(int id){
        userService.lock(id);
        return R.success("操作成功");
    }

    @GetMapping("/findById")
    public R<User> findById(int id){
        User user = userService.getById(id);
        return R.success(user);
    }


    @PostMapping("/save")
    public R<String> save(@RequestBody User user){
        user.setCreateTime(LocalDateTime.now());
        userService.save(user);
        return R.success("操作成功");
    }

    @PostMapping("/update")
    public R<String> update(@RequestBody User user){
        userService.updateById(user);
        return R.success("操作成功");
    }



}

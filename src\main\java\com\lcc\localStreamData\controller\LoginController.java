package com.lcc.localStreamData.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lcc.localStreamData.common.R;
import com.lcc.localStreamData.entity.User;
import com.lcc.localStreamData.entity.dto.UserLoginDto;
import com.lcc.localStreamData.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
public class LoginController {

    @Autowired
    private UserService userService;

    @PostMapping("/login")
    public R<User> login(HttpServletRequest request, @RequestBody UserLoginDto userLoginDto) {
//        log.info("login params:{}", userLoginDto);
        //优先检测验证码
        String realCode = (String) request.getSession().getAttribute(userLoginDto.getUuid());
        if (!StringUtils.isNotEmpty(realCode) || !realCode.equals(userLoginDto.getCode())) {
            return R.error("验证码错误");
        }
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", userLoginDto.getUsername());
        User one = userService.getOne(queryWrapper);
        if (one == null) {
            return R.error("账号不存在");
        }

        if (!one.getPassword().equals(userLoginDto.getPassword())) {
            return R.error("用户名或密码错误");
        }

        if (one.getIsActive().equals("0")) {
            return R.error("账号已禁用，请联系管理员");
        }


        //登录成功，清除session中的验证码，将用户信息存入session
        request.getSession().removeAttribute(userLoginDto.getUuid());
        request.getSession().setAttribute("user", one.getId());
        return R.success(one);
    }

    @PostMapping("/logout")
    public R<String> logout(HttpServletRequest request) {
        request.getSession().removeAttribute("user");
        return R.success("退出成功");
    }

}

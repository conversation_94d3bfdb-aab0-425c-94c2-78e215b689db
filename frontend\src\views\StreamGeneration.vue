<template>
  <div class="stream-generation">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>流数据生成与处理</span>
          <div>
            <el-button type="primary" @click="startStream" :loading="isStreaming">
              {{ isStreaming ? '流处理中...' : '开始流处理' }}
            </el-button>
            <el-button @click="stopStream" :disabled="!isStreaming">停止</el-button>
          </div>
        </div>
      </template>

      <!-- 流处理配置 -->
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card shadow="never">
            <template #header>
              <h4>数据源配置</h4>
            </template>
            <el-form :model="streamConfig" label-width="100px">
              <el-form-item label="数据源类型">
                <el-select v-model="streamConfig.sourceType" placeholder="选择数据源">
                  <el-option label="Kafka" value="kafka" />
                  <el-option label="文件流" value="file" />
                  <el-option label="模拟数据" value="mock" />
                  <el-option label="数据库" value="database" />
                </el-select>
              </el-form-item>
              <el-form-item label="批处理大小">
                <el-input-number v-model="streamConfig.batchSize" :min="1" :max="1000" />
              </el-form-item>
              <el-form-item label="处理间隔">
                <el-select v-model="streamConfig.interval" placeholder="选择处理间隔">
                  <el-option label="1秒" value="1000" />
                  <el-option label="5秒" value="5000" />
                  <el-option label="10秒" value="10000" />
                  <el-option label="30秒" value="30000" />
                </el-select>
              </el-form-item>
              <el-form-item label="数据格式">
                <el-radio-group v-model="streamConfig.format">
                  <el-radio label="json">JSON</el-radio>
                  <el-radio label="csv">CSV</el-radio>
                  <el-radio label="avro">Avro</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card shadow="never">
            <template #header>
              <h4>处理管道配置</h4>
            </template>
            <div class="pipeline-config">
              <el-checkbox-group v-model="streamConfig.processors">
                <div class="processor-item">
                  <el-checkbox label="validation">数据验证</el-checkbox>
                  <el-tooltip content="验证数据格式和完整性" placement="right">
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </div>
                <div class="processor-item">
                  <el-checkbox label="enrichment">数据增强</el-checkbox>
                  <el-tooltip content="添加地理位置、时间等信息" placement="right">
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </div>
                <div class="processor-item">
                  <el-checkbox label="normalization">数据标准化</el-checkbox>
                  <el-tooltip content="统一数据格式和单位" placement="right">
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </div>
                <div class="processor-item">
                  <el-checkbox label="filtering">数据过滤</el-checkbox>
                  <el-tooltip content="过滤无效或重复数据" placement="right">
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </div>
                <div class="processor-item">
                  <el-checkbox label="aggregation">数据聚合</el-checkbox>
                  <el-tooltip content="按时间窗口聚合数据" placement="right">
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </div>
              </el-checkbox-group>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card shadow="never">
            <template #header>
              <h4>输出配置</h4>
            </template>
            <el-form :model="streamConfig" label-width="100px">
              <el-form-item label="输出目标">
                <el-checkbox-group v-model="streamConfig.outputs">
                  <el-checkbox label="elasticsearch">Elasticsearch</el-checkbox>
                  <el-checkbox label="kafka">Kafka Topic</el-checkbox>
                  <el-checkbox label="database">数据库</el-checkbox>
                  <el-checkbox label="file">文件系统</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="数据保留">
                <el-select v-model="streamConfig.retention" placeholder="选择保留时间">
                  <el-option label="1天" value="1d" />
                  <el-option label="7天" value="7d" />
                  <el-option label="30天" value="30d" />
                  <el-option label="永久" value="forever" />
                </el-select>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>

      <!-- 流处理监控 -->
      <div class="stream-monitoring">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <h4>处理统计</h4>
              </template>
              <div class="monitoring-stats">
                <div class="stat-row">
                  <span class="stat-label">处理速率:</span>
                  <span class="stat-value">{{ streamStats.throughput }} 条/秒</span>
                </div>
                <div class="stat-row">
                  <span class="stat-label">总处理量:</span>
                  <span class="stat-value">{{ streamStats.totalProcessed }}</span>
                </div>
                <div class="stat-row">
                  <span class="stat-label">成功率:</span>
                  <span class="stat-value">{{ streamStats.successRate }}%</span>
                </div>
                <div class="stat-row">
                  <span class="stat-label">错误数:</span>
                  <span class="stat-value error">{{ streamStats.errorCount }}</span>
                </div>
                <div class="stat-row">
                  <span class="stat-label">延迟:</span>
                  <span class="stat-value">{{ streamStats.latency }}ms</span>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <h4>处理日志</h4>
              </template>
              <div class="log-container">
                <div 
                  v-for="log in processLogs.slice(-10)" 
                  :key="log.id" 
                  class="log-item"
                  :class="log.level"
                >
                  <span class="log-time">{{ log.timestamp }}</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 数据流可视化 -->
      <div class="stream-visualization">
        <el-card shadow="never">
          <template #header>
            <h4>数据流可视化</h4>
          </template>
          <div class="flow-diagram">
            <div class="flow-node source">
              <div class="node-title">数据源</div>
              <div class="node-content">{{ streamConfig.sourceType }}</div>
            </div>
            <div class="flow-arrow">→</div>
            <div class="flow-node processor">
              <div class="node-title">处理管道</div>
              <div class="node-content">{{ streamConfig.processors.length }} 个处理器</div>
            </div>
            <div class="flow-arrow">→</div>
            <div class="flow-node output">
              <div class="node-title">输出目标</div>
              <div class="node-content">{{ streamConfig.outputs.length }} 个目标</div>
            </div>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { QuestionFilled } from '@element-plus/icons-vue'

const isStreaming = ref(false)
const streamTimer = ref(null)

const streamConfig = reactive({
  sourceType: 'mock',
  batchSize: 100,
  interval: '5000',
  format: 'json',
  processors: ['validation', 'enrichment'],
  outputs: ['elasticsearch', 'database'],
  retention: '7d'
})

const streamStats = reactive({
  throughput: 0,
  totalProcessed: 0,
  successRate: 100,
  errorCount: 0,
  latency: 0
})

const processLogs = ref([])

// 添加日志
const addLog = (level, message) => {
  const log = {
    id: Date.now(),
    timestamp: new Date().toLocaleTimeString(),
    level,
    message
  }
  processLogs.value.push(log)
  if (processLogs.value.length > 100) {
    processLogs.value.shift()
  }
}

// 模拟流处理
const processStream = () => {
  const processed = Math.floor(Math.random() * streamConfig.batchSize) + 50
  const errors = Math.floor(Math.random() * 3)
  
  streamStats.totalProcessed += processed
  streamStats.errorCount += errors
  streamStats.throughput = Math.floor(processed / (parseInt(streamConfig.interval) / 1000))
  streamStats.successRate = Math.floor(((processed - errors) / processed) * 100)
  streamStats.latency = Math.floor(Math.random() * 100) + 10
  
  addLog('info', `处理了 ${processed} 条数据，${errors} 个错误`)
  
  if (errors > 0) {
    addLog('error', `处理过程中发生 ${errors} 个错误`)
  }
}

// 开始流处理
const startStream = () => {
  if (isStreaming.value) return
  
  isStreaming.value = true
  addLog('info', '开始流数据处理')
  
  streamTimer.value = setInterval(() => {
    processStream()
  }, parseInt(streamConfig.interval))
  
  ElMessage.success('流处理已启动')
}

// 停止流处理
const stopStream = () => {
  if (streamTimer.value) {
    clearInterval(streamTimer.value)
    streamTimer.value = null
  }
  isStreaming.value = false
  addLog('info', '流数据处理已停止')
  ElMessage.info('流处理已停止')
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pipeline-config {
  padding: 10px 0;
}

.processor-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.processor-item .el-icon {
  margin-left: 8px;
  color: #909399;
  cursor: help;
}

.stream-monitoring {
  margin: 20px 0;
}

.monitoring-stats {
  padding: 10px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-label {
  color: #666;
}

.stat-value {
  font-weight: bold;
  color: #409EFF;
}

.stat-value.error {
  color: #F56C6C;
}

.log-container {
  height: 300px;
  overflow-y: auto;
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  margin-bottom: 5px;
  padding: 5px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 12px;
}

.log-item.info {
  background: #e8f4fd;
  color: #409EFF;
}

.log-item.error {
  background: #fef0f0;
  color: #F56C6C;
}

.log-time {
  margin-right: 10px;
  color: #999;
}

.stream-visualization {
  margin-top: 20px;
}

.flow-diagram {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.flow-node {
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  min-width: 120px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.flow-node.source {
  background: #e8f4fd;
  border: 2px solid #409EFF;
}

.flow-node.processor {
  background: #f0f9ff;
  border: 2px solid #67C23A;
}

.flow-node.output {
  background: #fef0f0;
  border: 2px solid #E6A23C;
}

.node-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.node-content {
  color: #666;
  font-size: 14px;
}

.flow-arrow {
  font-size: 24px;
  margin: 0 20px;
  color: #409EFF;
}
</style>

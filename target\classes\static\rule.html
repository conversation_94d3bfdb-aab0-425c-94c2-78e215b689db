<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/elementui.css">
    <style>
        body {
            background-color: #fff;
        }

        #app {
            padding: 20px;
        }

        .page {
            margin-top: 30px;
            text-align: center;
        }

        .header {

        }

        .header .search {

        }

        .table {
            border: 2px solid #f3f4f7;
            margin-top: 20px;
        }

        .bread {
            margin-bottom: 20px;
        }

        .switch {
            margin-top: 8px;
        }
    </style>
</head>
<body>
<div id="app">

    <div class="bread">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item><a href="/">首页</a></el-breadcrumb-item>
            <el-breadcrumb-item><a href="/">规则管理</a></el-breadcrumb-item>
        </el-breadcrumb>
    </div>

    <div class="header">
        <el-row>
            <el-col :span="4">
                <el-input
                        placeholder="请输入规则组名称"
                        v-model="name"
                        clearable
                        @keyup.enter.native="handleSearch"></el-input>
            </el-col>
            <el-col :span="3">
                <el-button icon="el-icon-search" @click="handleSearch"></el-button>
            </el-col>
            <el-col :span="3">
                <el-button type="default" @click="handleAdd()">新增规则组</el-button>
            </el-col>
        </el-row>


    </div>
    <div class="table">
        <el-table
                :data="tableData"
                stripe
                style="width: 100%">
            <el-table-column
                    type="index"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="name"
                    label="规则组名称"
                    width="280"
                    :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column
                    prop="createTime"
                    label="创建时间"
                    width="150"
                    :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column label="操作">
                <template slot-scope="scope">
                    <el-button type="text" @click="preview(scope.row)">查看详情</el-button>
                    <el-button type="primary" size="medium" @click="handleEdit(scope.row)">编辑</el-button>
                    <el-button type="danger" size="medium" @click="handleDelete(scope.row.id)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>

    <el-dialog :title="ruleGroupInfo.id==null?'添加规则组':'编辑规则组'" :visible.sync="dialogFormVisible"
               width="700px">
        <el-form ref="ruleForm" :model="ruleGroupInfo" :rules="rules" :inline="true">
            <el-form-item label="规则组名称" prop="name" label-width="100px"
                          :rules="rules.name">
                <el-input v-model="ruleGroupInfo.name" autocomplete="off" placeholder="请填写规则组名称">
                </el-input>
            </el-form-item>
            <!--<div v-if="ruleGroupInfo.id!=null">
                {{ruleGroupInfo.createTime}}
            </div>-->
            <!--<el-form-item v-if="ruleGroupInfo.id!=null" label="创建人" label-width="150px">
                {{ruleGroupInfo.createUser}}
            </el-form-item>-->
            <div style="font-size: 18px;font-weight: bold">规则列表:</div>
            <hr/>
            <div v-for="(item,index) in ruleGroupInfo.ruleList" :key="index">
                <div style="font-weight: bold">规则{{index+1}}</div>
                <el-form-item>
                    规则类型：
                    <el-select v-model="ruleGroupInfo.ruleList[index].type" placeholder="请选择">
                        <el-option key="1" label="高频消费" value="1"></el-option>
                        <el-option key="2" label="异地消费" value="2"></el-option>
                    </el-select>
                </el-form-item>
                <div v-if="item.type=='1'"><!--高频消费-->
                    <el-form-item style="width:200px;">
                        <el-input v-model="ruleGroupInfo.ruleList[index].timeLimit"
                                  placeholder="请输入时间"></el-input>
                    </el-form-item>
                    <span style="line-height: 40px;">秒内消费&nbsp;&nbsp;</span>
                    <el-form-item style="width:200px;">
                        <el-input v-model="ruleGroupInfo.ruleList[index].freqLimit"
                                  placeholder="请输入次数"></el-input>
                    </el-form-item>
                    <span style="line-height: 40px;">次则视为异常</span>
                </div>
                <div v-else-if="item.type=='2'"><!--异地消费-->
                    <el-form-item style="width:200px;">
                        <el-input v-model="ruleGroupInfo.ruleList[index].timeLimit" maxlength="10"
                                  placeholder="请输入时间"></el-input>
                    </el-form-item>
                    <span style="line-height: 40px;">秒内在不同城市进行过消费则视为异常</span>
                </div>
                <hr/>
            </div>
            <div style="width: 100%;display: flex;justify-content: center;">
                <el-button type="primary" @click="addRule" round><i class="el-icon-plus"></i></el-button>
                <el-button type="danger" @click="popRule" round><i class="el-icon-minus"></i></el-button>
            </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <!--            <el-button type="primary" @click="submitModel">提交至本地</el-button>-->
            <el-button type="primary" @click="submitForm('ruleForm')">确定</el-button>
            <el-button @click="dialogFormVisible = false;">取 消</el-button>
        </div>
    </el-dialog>

    <el-dialog title="规则组详情" :visible.sync="previewDialogVisible" width="700px">
        <div style="font-weight: bold;padding-bottom: 10px;">{{ruleGroupInfo.name}}</div>
        <div style="padding-bottom: 10px;">创建时间：{{ruleGroupInfo.createTime}}</div>
        <!--<div style="font-weight: bold">创建人：{{ruleGroupInfo.createUser}}</div>-->
        <el-table
                :data="ruleGroupInfo.ruleList"
                stripe
                style="width: 100%">
            <el-table-column
                    type="index"
                    :index="detailIndexMethod"
                    width="100">
            </el-table-column>
            <el-table-column
                    label="规则详情"
                    width="500">
                <template slot-scope="scope">
                    <span v-if="scope.row.type=='1'">
                        {{scope.row.timeLimit}}秒内消费{{scope.row.freqLimit}}次则视为异常
                    </span>
                    <span v-else-if="scope.row.type=='2'">
                        {{scope.row.timeLimit}}秒内在不同城市进行过消费则视为异常
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
            <!--            <el-button type="primary" @click="submitModel">提交至本地</el-button>-->
            <el-button @click="previewDialogVisible = false;">取 消</el-button>
        </div>
    </el-dialog>


    <div class="page">
        <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="page"
                :page-sizes="[9, 20, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalData">
        </el-pagination>
    </div>

</div>
</body>
<script src="js/vue.js"></script>
<script src="js/axios.js"></script>
<script src="js/elementui.js"></script>
<script src="js/moment.js"></script>
<script src="js/jquery.min.js"></script>
<script>
    new Vue({
        el: "#app",
        data() {
            return {
                tableData: [],
                page: 1,
                pageSize: 9,
                totalData: 400,
                condition: "",
                name: "",
                opt: "",
                dialogFormVisible: false,
                previewDialogVisible: false,
                datasetInfo: {
                    datasetName: "",
                    filename: "",
                    size: 0
                },
                ruleGroupInfo: {
                    id: null,
                    name: "",
                    createUser: null,
                    createTime: "",
                    ruleList: []
                },
                rules: {
                    name: [
                        {required: true, message: '请输入规则组名称', trigger: 'blur'},
                    ],
                    ruleList: {
                        type: [
                            {required: true, message: '请选择类型'}
                        ],
                        timeLimit: [
                            {required: true, message: '请输入时间', trigger: 'blur'}
                        ],
                        freqLimit: [
                            {required: true, message: '请输入次数', trigger: 'blur'}
                        ],
                    },
                },
            }
        },
        methods: {
            detailIndexMethod(index) {
                return "规则" + (index + 1);
            },
            handleSizeChange(val) {
                this.pageSize = val;
                this.page = 1;
                this.getList();
            },
            handleCurrentChange(val) {
                this.page = val;
                this.getList();
            },
            handleSearch() {
                this.page = 1;
                this.getList();
            },
            handleDelete(id) {
                // console.log(id)
                this.$confirm('确认删除规则组吗?这将同时删除其中的所有规则?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let that = this;
                    axios.delete(`/ruleGroup/${id}`).then(response => {//删除数据库记录
                        if (response.data.code == 1) {
                            that.$message({
                                message: '删除成功',
                                type: 'success'
                            })
                            that.getList();
                        } else {
                            that.$message.error(response.data.msg);
                        }
                    })
                })
            },
            handleAdd() {
                this.ruleGroupInfo.id = null;
                this.ruleGroupInfo.name = "";
                this.ruleGroupInfo.createTime = null;
                this.ruleGroupInfo.createUser = null;
                this.ruleGroupInfo.ruleList = [{
                    type: null,
                    timeLimit: null,
                    freqLimit: null
                }];
                this.dialogFormVisible = true;
            },
            handleEdit(row) {
                let that = this;
                axios.get("/ruleGroup/" + row.id).then(response => {
                    let ruleGroupInfo = response.data.data;
                    that.ruleGroupInfo.id = ruleGroupInfo.id;
                    that.ruleGroupInfo.name = ruleGroupInfo.name;
                    that.ruleGroupInfo.createTime = ruleGroupInfo.createTime;
                    that.ruleGroupInfo.createUser = ruleGroupInfo.createUser;
                    that.ruleGroupInfo.ruleList = ruleGroupInfo.ruleList;
                    that.dialogFormVisible = true;
                })
            },
            addRule() {
                this.ruleGroupInfo.ruleList.push({
                    type: null,
                    timeLimit: null,
                    freqLimit: null
                });
            },
            popRule() {
                this.ruleGroupInfo.ruleList.pop();
            },
            submitForm(formName) {
                console.log(this.ruleGroupInfo)
                this.$refs[formName].validate((valid) => {//当前只能对name字段校验
                    //TODO 对整个表单进行校验
                    if (!valid) {
                        // this.$message.error("输入有误")
                        return;
                    }
                    if (this.ruleGroupInfo.id == null) {
                        axios.post("/ruleGroup", this.ruleGroupInfo).then(response => {
                            if (response.data.code == 1) {
                                this.$message({
                                    message: "添加成功",
                                    type: 'success',
                                })
                                this.dialogFormVisible = false;
                                this.getList();
                            } else {
                                this.$message.error(response.data.msg);
                            }
                        })
                    } else {
                        axios.put("/ruleGroup/" + this.ruleGroupInfo.id, this.ruleGroupInfo).then(response => {
                            if (response.data.code == 1) {
                                this.$message({
                                    message: "修改成功",
                                    type: 'success'
                                })
                                this.dialogFormVisible = false;
                                this.getList();
                            } else {
                                this.$message.error(response.data.msg);
                            }
                        })
                    }
                })
            },
            goBack() {
                console.log('go back');
            },
            preview(row) {
                this.ruleGroupInfo.name = row.name;
                this.ruleGroupInfo.createUser = row.createUser;
                this.ruleGroupInfo.createTime = row.createTime;
                this.ruleGroupInfo.ruleList = row.ruleList;
                this.previewDialogVisible = true;
            },
            getList() {
                axios.get(`/ruleGroup/list?page=${this.page}&pageSize=${this.pageSize}&name=${this.name}`)
                    .then(response => {
                        let data = response.data.data;
                        //格式转换
                        for (let i = 0; i < data.records.length; i++) {
                            data.records[i].createTime = moment(data.records[i].createTime).format('YYYY-MM-DD HH:mm:ss');
                        }
                        this.totalData = data.total;
                        this.tableData = data.records;

                        console.log(this.tableData);
                    })
            },
        },
        created() {
            this.getList();
        }
        ,
    })
    ;
</script>
</html>
<template>
  <div class="real-time-assessment">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>风险实时评估与预警</span>
          <div>
            <el-button type="primary" @click="startMonitoring" :loading="isMonitoring">
              {{ isMonitoring ? '监控中...' : '开始监控' }}
            </el-button>
            <el-button @click="stopMonitoring" :disabled="!isMonitoring">停止监控</el-button>
          </div>
        </div>
      </template>

      <!-- 实时监控面板 -->
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card shadow="never">
            <template #header>
              <h4>实时统计</h4>
            </template>
            <div class="real-time-stats">
              <div class="stat-item">
                <div class="stat-value">{{ realtimeStats.totalTransactions }}</div>
                <div class="stat-label">总交易数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value risk">{{ realtimeStats.riskTransactions }}</div>
                <div class="stat-label">风险交易</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ realtimeStats.alertsTriggered }}</div>
                <div class="stat-label">触发预警</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ realtimeStats.processingSpeed }}</div>
                <div class="stat-label">处理速度(笔/秒)</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card shadow="never">
            <template #header>
              <h4>风险等级分布</h4>
            </template>
            <div class="risk-distribution">
              <div class="risk-level high">
                <div class="level-label">高风险</div>
                <div class="level-count">{{ riskDistribution.high }}</div>
                <el-progress :percentage="(riskDistribution.high / realtimeStats.totalTransactions * 100)" color="#F56C6C" />
              </div>
              <div class="risk-level medium">
                <div class="level-label">中风险</div>
                <div class="level-count">{{ riskDistribution.medium }}</div>
                <el-progress :percentage="(riskDistribution.medium / realtimeStats.totalTransactions * 100)" color="#E6A23C" />
              </div>
              <div class="risk-level low">
                <div class="level-label">低风险</div>
                <div class="level-count">{{ riskDistribution.low }}</div>
                <el-progress :percentage="(riskDistribution.low / realtimeStats.totalTransactions * 100)" color="#67C23A" />
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card shadow="never">
            <template #header>
              <h4>系统状态</h4>
            </template>
            <div class="system-status">
              <div class="status-item">
                <span class="status-label">模型状态:</span>
                <el-tag :type="systemStatus.modelStatus === 'online' ? 'success' : 'danger'">
                  {{ systemStatus.modelStatus === 'online' ? '在线' : '离线' }}
                </el-tag>
              </div>
              <div class="status-item">
                <span class="status-label">数据流状态:</span>
                <el-tag :type="systemStatus.dataStreamStatus === 'active' ? 'success' : 'warning'">
                  {{ systemStatus.dataStreamStatus === 'active' ? '活跃' : '空闲' }}
                </el-tag>
              </div>
              <div class="status-item">
                <span class="status-label">预警系统:</span>
                <el-tag :type="systemStatus.alertSystem === 'enabled' ? 'success' : 'info'">
                  {{ systemStatus.alertSystem === 'enabled' ? '启用' : '禁用' }}
                </el-tag>
              </div>
              <div class="status-item">
                <span class="status-label">响应时间:</span>
                <span class="response-time">{{ systemStatus.responseTime }}ms</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 实时交易流 -->
      <div class="real-time-stream">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <h4>实时交易流</h4>
              <el-switch v-model="autoScroll" active-text="自动滚动" />
            </div>
          </template>
          <div class="transaction-stream" ref="streamContainer">
            <div 
              v-for="transaction in realtimeTransactions" 
              :key="transaction.id"
              class="transaction-item"
              :class="getRiskClass(transaction.riskLevel)"
            >
              <div class="transaction-info">
                <span class="transaction-id">{{ transaction.id }}</span>
                <span class="transaction-amount">￥{{ transaction.amount }}</span>
                <span class="transaction-location">{{ transaction.location }}</span>
                <span class="transaction-time">{{ transaction.timestamp }}</span>
              </div>
              <div class="risk-info">
                <el-tag :type="getRiskTagType(transaction.riskLevel)" size="small">
                  {{ getRiskLevelText(transaction.riskLevel) }}
                </el-tag>
                <span class="risk-score">{{ transaction.riskScore }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 预警配置 -->
      <div class="alert-configuration">
        <el-card shadow="never">
          <template #header>
            <h4>预警配置</h4>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form :model="alertConfig" label-width="120px">
                <el-form-item label="高风险阈值">
                  <el-slider v-model="alertConfig.highRiskThreshold" :min="70" :max="100" show-input />
                </el-form-item>
                <el-form-item label="中风险阈值">
                  <el-slider v-model="alertConfig.mediumRiskThreshold" :min="40" :max="70" show-input />
                </el-form-item>
                <el-form-item label="预警方式">
                  <el-checkbox-group v-model="alertConfig.alertMethods">
                    <el-checkbox label="email">邮件通知</el-checkbox>
                    <el-checkbox label="sms">短信通知</el-checkbox>
                    <el-checkbox label="webhook">Webhook</el-checkbox>
                    <el-checkbox label="dashboard">仪表板提醒</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label="预警频率">
                  <el-select v-model="alertConfig.alertFrequency">
                    <el-option label="立即" value="immediate" />
                    <el-option label="每分钟" value="minute" />
                    <el-option label="每5分钟" value="5minutes" />
                    <el-option label="每小时" value="hour" />
                  </el-select>
                </el-form-item>
              </el-form>
            </el-col>
            <el-col :span="12">
              <h5>最近预警</h5>
              <div class="recent-alerts">
                <div 
                  v-for="alert in recentAlerts" 
                  :key="alert.id"
                  class="alert-item"
                  :class="alert.level"
                >
                  <div class="alert-header">
                    <span class="alert-title">{{ alert.title }}</span>
                    <span class="alert-time">{{ alert.timestamp }}</span>
                  </div>
                  <div class="alert-content">{{ alert.message }}</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 风险趋势图表 -->
      <div class="risk-trends">
        <el-card shadow="never">
          <template #header>
            <h4>风险趋势分析</h4>
          </template>
          <div ref="trendChartRef" style="height: 300px;"></div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

const isMonitoring = ref(false)
const autoScroll = ref(true)
const streamContainer = ref()
const trendChartRef = ref()
const monitoringTimer = ref(null)

const realtimeStats = reactive({
  totalTransactions: 0,
  riskTransactions: 0,
  alertsTriggered: 0,
  processingSpeed: 0
})

const riskDistribution = reactive({
  high: 0,
  medium: 0,
  low: 0
})

const systemStatus = reactive({
  modelStatus: 'online',
  dataStreamStatus: 'active',
  alertSystem: 'enabled',
  responseTime: 45
})

const alertConfig = reactive({
  highRiskThreshold: 80,
  mediumRiskThreshold: 50,
  alertMethods: ['email', 'dashboard'],
  alertFrequency: 'immediate'
})

const realtimeTransactions = ref([])
const recentAlerts = ref([])

// 生成模拟交易数据
const generateTransaction = () => {
  const riskScore = Math.floor(Math.random() * 100)
  let riskLevel = 'low'
  if (riskScore >= alertConfig.highRiskThreshold) riskLevel = 'high'
  else if (riskScore >= alertConfig.mediumRiskThreshold) riskLevel = 'medium'
  
  const transaction = {
    id: `TXN${Date.now()}${Math.floor(Math.random() * 1000)}`,
    amount: Math.floor(Math.random() * 50000) + 100,
    location: ['北京', '上海', '广州', '深圳', '杭州'][Math.floor(Math.random() * 5)],
    timestamp: new Date().toLocaleTimeString(),
    riskScore,
    riskLevel
  }
  
  realtimeTransactions.value.unshift(transaction)
  if (realtimeTransactions.value.length > 50) {
    realtimeTransactions.value.pop()
  }
  
  // 更新统计
  realtimeStats.totalTransactions++
  if (riskLevel === 'high' || riskLevel === 'medium') {
    realtimeStats.riskTransactions++
  }
  
  riskDistribution[riskLevel]++
  
  // 触发预警
  if (riskLevel === 'high') {
    triggerAlert(transaction)
  }
  
  // 自动滚动
  if (autoScroll.value) {
    nextTick(() => {
      if (streamContainer.value) {
        streamContainer.value.scrollTop = 0
      }
    })
  }
}

// 触发预警
const triggerAlert = (transaction) => {
  realtimeStats.alertsTriggered++
  
  const alert = {
    id: Date.now(),
    level: 'high',
    title: '高风险交易预警',
    message: `检测到高风险交易：${transaction.id}，金额：￥${transaction.amount}，风险评分：${transaction.riskScore}`,
    timestamp: new Date().toLocaleTimeString()
  }
  
  recentAlerts.value.unshift(alert)
  if (recentAlerts.value.length > 10) {
    recentAlerts.value.pop()
  }
  
  ElMessage.warning(`高风险交易预警：${transaction.id}`)
}

// 开始监控
const startMonitoring = () => {
  if (isMonitoring.value) return
  
  isMonitoring.value = true
  realtimeStats.processingSpeed = Math.floor(Math.random() * 50) + 20
  
  monitoringTimer.value = setInterval(() => {
    generateTransaction()
  }, 1000)
  
  ElMessage.success('实时监控已启动')
}

// 停止监控
const stopMonitoring = () => {
  if (monitoringTimer.value) {
    clearInterval(monitoringTimer.value)
    monitoringTimer.value = null
  }
  isMonitoring.value = false
  realtimeStats.processingSpeed = 0
  
  ElMessage.info('实时监控已停止')
}

// 获取风险等级样式类
const getRiskClass = (level) => {
  return `risk-${level}`
}

// 获取风险标签类型
const getRiskTagType = (level) => {
  switch (level) {
    case 'high': return 'danger'
    case 'medium': return 'warning'
    case 'low': return 'success'
    default: return 'info'
  }
}

// 获取风险等级文本
const getRiskLevelText = (level) => {
  switch (level) {
    case 'high': return '高风险'
    case 'medium': return '中风险'
    case 'low': return '低风险'
    default: return '未知'
  }
}

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChartRef.value) return
  
  const chart = echarts.init(trendChartRef.value)
  const option = {
    title: {
      text: '风险交易趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['高风险', '中风险', '低风险']
    },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 24 }, (_, i) => `${i}:00`)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '高风险',
        type: 'line',
        data: Array.from({ length: 24 }, () => Math.floor(Math.random() * 20)),
        itemStyle: { color: '#F56C6C' }
      },
      {
        name: '中风险',
        type: 'line',
        data: Array.from({ length: 24 }, () => Math.floor(Math.random() * 50)),
        itemStyle: { color: '#E6A23C' }
      },
      {
        name: '低风险',
        type: 'line',
        data: Array.from({ length: 24 }, () => Math.floor(Math.random() * 100) + 50),
        itemStyle: { color: '#67C23A' }
      }
    ]
  }
  chart.setOption(option)
}

onMounted(() => {
  nextTick(() => {
    initTrendChart()
  })
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.real-time-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-value.risk {
  color: #F56C6C;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.risk-distribution {
  padding: 10px 0;
}

.risk-level {
  margin-bottom: 15px;
}

.level-label {
  font-weight: bold;
  margin-bottom: 5px;
}

.level-count {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}

.system-status {
  padding: 10px 0;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.status-label {
  color: #666;
}

.response-time {
  font-weight: bold;
  color: #409EFF;
}

.real-time-stream {
  margin: 20px 0;
}

.transaction-stream {
  height: 400px;
  overflow-y: auto;
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  margin-bottom: 5px;
  background: white;
  border-radius: 4px;
  border-left: 4px solid #67C23A;
}

.transaction-item.risk-medium {
  border-left-color: #E6A23C;
}

.transaction-item.risk-high {
  border-left-color: #F56C6C;
}

.transaction-info {
  display: flex;
  gap: 15px;
}

.transaction-id {
  font-family: monospace;
  font-weight: bold;
}

.risk-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.risk-score {
  font-weight: bold;
}

.alert-configuration {
  margin: 20px 0;
}

.recent-alerts {
  height: 300px;
  overflow-y: auto;
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
}

.alert-item {
  padding: 10px;
  margin-bottom: 10px;
  background: white;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
}

.alert-item.high {
  border-left-color: #F56C6C;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.alert-title {
  font-weight: bold;
}

.alert-time {
  color: #666;
  font-size: 12px;
}

.alert-content {
  color: #666;
  font-size: 14px;
}

.risk-trends {
  margin-top: 20px;
}
</style>

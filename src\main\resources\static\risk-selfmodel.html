<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/elementui.css">
    <style>
        body {
            background-color: #fff;
        }

        #app {
            padding: 20px;
        }

        .form {
            width: 1200px;
            margin: 55px auto;
        }
    </style>
</head>
<body>
<div id="app">

    <div class="bread">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item><a href="/">首页</a></el-breadcrumb-item>
            <el-breadcrumb-item><a href="/">模型评估</a></el-breadcrumb-item>
        </el-breadcrumb>
    </div>

    <div class="form">
        <el-form :model="modelForm" :rules="rules" ref="modelForm" label-width="140px" class="demo-modelForm">
            <el-form-item label="选择模型" prop="modelId">
                <el-select v-model="modelForm.modelId" placeholder="请选择评估模型" style="width: 400px">
                    <el-option v-for="item in modelList" :key="item.value" :label="item.label" :value="item.value">
                        {{ item.label }}
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="请选择评估数据集">
                <el-table
                        ref="multipleTable"
                        :data="tableData"
                        tooltip-effect="dark"
                        style="width: 100%"
                        @selection-change="handleSelectionChange"
                        max-height="550">
                    <el-table-column
                            type="selection"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            prop="streamTime"
                            label="消费时间"
                            width="280">
                    </el-table-column>
                    <el-table-column
                            prop="streamMoney"
                            label="消费金额(元)"
                            width="280">
                    </el-table-column>
                    <el-table-column
                            prop="streamSignLocation"
                            label="注册地"
                            width="280">
                    </el-table-column>
                    <el-table-column
                            prop="streamConsumeLocation"
                            label="消费地">
                    </el-table-column>
                </el-table>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="submitForm('modelForm')">立即评估</el-button>
                <el-button @click="resetForm('modelForm')">重置</el-button>
            </el-form-item>
        </el-form>
    </div>

    <div
            v-loading.fullscreen.lock="fullscreenLoading"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)">
    </div>

</div>
</body>
<script src="js/vue.js"></script>
<script src="js/axios.js"></script>
<script src="js/elementui.js"></script>
<script src="js/moment.js"></script>
<script src="js/jquery.min.js"></script>
<script>
    const remoteIp="************";
    new Vue({
        el: "#app",
        data() {
            return {
                fullscreenLoading: false,
                tableData: [],
                modelList: [],
                dataIds: [],
                modelForm: {
                    modelId: '',
                    dataIds: ''
                },
                rules: {
                    model: [
                        {required: true, message: '请选择模型', trigger: 'change'}
                    ]
                }
            }
        },
        methods: {
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid && this.dataIds.length > 0) {
                        // 处理 dataIds，将其连接成一个以逗号分隔的字符串
                        this.modelForm.dataIds = this.dataIds.map(item => item.streamId).join(',');

                        var that = this;
                        this.fullscreenLoading = true;

                        // 发送请求
                        $.ajax({
                            type: 'post',
                            // url: `http://************:10408/${that.ruleForm.method[0]}/test`,
                            url: `http://`+remoteIp+`:10408/model/test`,
                            data: this.modelForm,
                            success: function (res) {
                                localStorage.setItem("dataIds", that.modelForm.dataIds)
                                location.href = "risk-result.html"
                                that.fullscreenLoading = false;
                            }
                        })

                        console.log(this.modelForm);
                    } else {
                        alert("请完善表单");
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            resetForm(formName) {
                this.$refs[formName].resetFields();
            },
            handleSelectionChange(val) {
                console.log(val);
                this.dataIds = val;
            },
            getList() {
                axios.get(`/risk/list`).then(response => {
                    let data = response.data.data;
                    // 格式转换
                    for (let i = 0; i < data.length; i++) {
                        data[i].streamTime = moment(data[i].streamTime).format('YYYY-MM-DD HH:mm:ss');
                        data[i].streamConsumeLocation = data[i].streamConsumeLocation.replaceAll(",", "-");
                    }
                    this.tableData = data;
                });
            },
            getModelList() {
                let userInfo = window.localStorage.getItem('userInfo');
                // console.log(JSON.parse(userInfo))
                let userId = userInfo != null ? JSON.parse(userInfo).id : ""
                axios.get(`/models/list/user?page=${1}&pageSize=${1000}&trainerId=${userId}`).then(response => {
                    if (response.data.code === 1) {
                        this.modelList = response.data.data.records.map(model => ({
                            value: model.number,
                            label: model.modelName
                        }));
                    } else {
                        this.$message.error(response.data.msg);
                    }
                });
            }
        },
        created() {
            this.getList();
            this.getModelList();
        }
    });
</script>
</html>

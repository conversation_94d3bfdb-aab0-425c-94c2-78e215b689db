<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        .el-header {
            background-color: #B3C0D1;
            color: #333;
            line-height: 60px;
        }

        .el-aside {
            color: #333;
        }

        .el-main{
            padding: 10px !important;
            background-color: #f3f4f7;
        }

    </style>
</head>
<body>
<div id="app">
    <el-container style="height: 925px">
        <el-aside width="200px"  style="background-color: #061031">
            <img src="images/logo1.jpg" alt="" style="width: 100%;margin-bottom: 10px">
            <el-menu
                    default-active="2"
                    class="el-menu-vertical-demo"
                    @open="handleOpen"
                    @close="handleClose"
                    background-color="#061031"
                    text-color="#fff"
                    active-text-color="#ffd04b">
                <el-menu-item index="1">
                    <i class="el-icon-menu"></i>
                    <span slot="title">首页</span>
                </el-menu-item>
                <el-menu-item index="2" @click="iframeUrl='index.html'">
                    <i class="el-icon-menu"></i>
                    <span slot="title">大屏展示</span>
                </el-menu-item>
                <el-menu-item index="3" @click="iframeUrl='user.html'" disabled>
                    <i class="el-icon-document"></i>
                    <span slot="title">用户管理</span>
                </el-menu-item>
                <el-menu-item index="4" @click="iframeUrl='data.html'">
                    <i class="el-icon-setting"></i>
                    <span slot="title">数据管理</span>
                </el-menu-item>
                <el-menu-item index="5" @click="iframeUrl='model.html'">
                    <i class="el-icon-setting"></i>
                    <span slot="title">模型管理</span>
                </el-menu-item>
                <el-menu-item index="6" @click="iframeUrl='risk.html'">
                    <i class="el-icon-setting"></i>
                    <span slot="title">风险评估</span>
                </el-menu-item>
                <el-menu-item index="7" @click="iframeUrl='user-paint.html'">
                    <i class="el-icon-setting"></i>
                    <span slot="title">用户预警</span>
                </el-menu-item>
                <el-menu-item index="8" @click="iframeUrl='model.html'">
                    <i class="el-icon-setting"></i>
                    <span slot="title">平台监控</span>
                </el-menu-item>
            </el-menu>
        </el-aside>

        <el-container>

            <el-header style="font-size: 12px; background-color:#061031;color: white;">

                <el-row :gutter="20">
                    <el-col :span="16">
                        <div style="font-size: 24px;font-weight: bold;margin-left: 50%;letter-spacing: 3px;">
                            面向金融行业流数据的在线分析系统
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div style="text-align: right">
                            <el-dropdown>
                                <i class="el-icon-setting" style="margin-right: 15px"></i>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item>查看</el-dropdown-item>
                                    <el-dropdown-item>新增</el-dropdown-item>
                                    <el-dropdown-item>删除</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                            <span>王小虎</span>
                        </div>
                    </el-col>
                </el-row>



            </el-header>

            <el-main>

                    <iframe :src="iframeUrl" frameborder="0" width="100%" height="100%"></iframe>

            </el-main>

        </el-container>
    </el-container>
    <div style="background-color:#acabad;height: 5%;text-align: center;color: #2a2831;line-height: 30px;font-size: 16px;font-weight: bold">
        重庆邮电大学 大数倨智能计算创新研发团队@2015-2023星环信息科技(上海)股份有限公司&重庆邮电大学联合出品
    </div>

</div>
</body>
<script src="js/vue.js"></script>
<script src="js/axios.js"></script>
<script src="js/elementui.js"></script>
<script>
    let app = new Vue({
        el: "#app",
        data() {
            const item = {
                date: '2016-05-02',
                name: '王小虎',
                address: '上海市普陀区金沙江路 1518 弄'
            };
            return {
                tableData: Array(20).fill(item),
                iframeUrl: "index.html"
            }
        }
    });
</script>
</html>
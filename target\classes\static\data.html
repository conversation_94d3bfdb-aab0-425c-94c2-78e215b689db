<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/elementui.css">
    <style>
        body {
            background-color: #fff;
        }
        #app {
            padding: 20px;
        }
        .page {
            margin-top: 30px;
            text-align: center;
        }
        .header {

        }
        .header .search {
            width: 230px;
        }
        .table {
            border: 2px solid #f3f4f7;
            margin-top: 20px;
        }
        .bread {
            margin-bottom: 20px;
        }
        .switch {
            margin-top: 8px;
        }
    </style>
</head>
<body>
<div id="app">

    <div class="bread">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item><a href="/">首页</a></el-breadcrumb-item>
            <el-breadcrumb-item><a href="/">流数据管理</a></el-breadcrumb-item>
        </el-breadcrumb>
    </div>

    <div class="header">
        <el-row :gutter="20">
            <el-col :span="6">
                <div class="search">
                    <el-input
                            placeholder="请输入地点"
                            v-model="place"
                            clearable
                            @keyup.enter.native="handleSearch"
                            >
                        <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
                    </el-input>
                </div>
            </el-col>
            <el-col :span="6" :offset="12">

            </el-col>
        </el-row>


    </div>
    <div class="table">
        <el-table
                :data="tableData"
                stripe
                style="width: 100%">
            <el-table-column
                    type="index"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="streamTime"
                    label="消费时间"
                    width="320">
            </el-table-column>
            <el-table-column
                    prop="streamMoney"
                    label="消费金额(元)"
                    width="230">
            </el-table-column>
            <el-table-column
                    prop="streamSignLocation"
                    label="注册地"
                    width="250"
            >
            </el-table-column>
            <el-table-column
                    prop="streamConsumeLocation"
                    label="消费地"
                    width="320"
            >
            </el-table-column>
            <el-table-column
                    label="是否异常"
                    width="250"
            >
                <template slot-scope="scope">
                    <el-tag type="success" v-if="scope.row.streamIsNormal==1">正常消费</el-tag>
                    <el-tag type="danger" v-if="scope.row.streamIsNormal==0">异常消费</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    label="操作"
            >
                <template slot-scope="scope">
                    <el-button type="warning" icon="el-icon-warning-outline" circle @click="handleStream(scope.row.streamId)" :disabled="scope.row.streamIsNormal==0"></el-button>
                    <el-button type="danger" icon="el-icon-delete" circle @click="handleDelete(scope.row.streamId)"></el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>

    <div class="page">
        <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="page"
                :page-sizes="[9, 20, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalData">
        </el-pagination>
    </div>

</div>
</body>
<script src="js/vue.js"></script>
<script src="js/axios.js"></script>
<script src="js/elementui.js"></script>
<script src="js/moment.js"></script>
<script>
    new Vue({
        el: "#app",
        data() {
            return {
                tableData: [],
                page: 1,
                pageSize: 9,
                totalData: 400,
                condition: "",
                place: "",
                opt: ""
            }
        },
        methods: {
            handleSizeChange(val) {
                this.pageSize=val;
                this.page=1;
                this.getList();
            },
            handleCurrentChange(val) {
                this.page=val;
                this.getList();
            },
            handleSearch() {
                this.page=1;
                this.getList();
            },
            handleStream(id) {
                this.$confirm('此操作将标记为异常消费, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {


                    axios.get(`/stream/mark?id=${id}`).then(response => {

                        if (response.data.code==1){
                            this.$message({
                                type: 'success',
                                message: '操作成功!'
                            });
                            this.getList()
                        }else {
                            this.$message.error('操作失败');
                        }


                    })


                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消标记'
                    });
                });
            },
            handleDelete(id) {
                this.$confirm('此操作将永久删除该消费, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error'
                }).then(() => {
                    axios.get(`/stream/delete?id=${id}`).then(response => {

                        if (response.data.code==1){
                            this.$message({
                                type: 'success',
                                message: '操作成功!'
                            });
                            this.getList()
                        }else {
                            this.$message.error('操作失败');
                        }


                    })
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            },
            goBack() {
                console.log('go back');
            },
            getList() {
                axios.get(`/stream/list?page=${this.page}&pageSize=${this.pageSize}&place=${this.place}`)
                .then(response => {
                    // console.log(response.data)
                    let data = response.data.data;
                    //格式转换
                    for (let i = 0; i < data.records.length; i++) {
                        data.records[i].streamTime = moment(data.records[i].streamTime).format('YYYY-MM-DD HH:mm:ss');
                        data.records[i].streamConsumeLocation = data.records[i].streamConsumeLocation.replaceAll(",","-")
                    }
                    this.totalData = data.total;
                    this.tableData = data.records;

                    console.log(this.tableData);
                })
            }
        },
        created() {
            this.getList();
        },
    });
</script>
</html>
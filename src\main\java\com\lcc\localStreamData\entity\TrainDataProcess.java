package com.lcc.localStreamData.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class TrainDataProcess implements Serializable {
    @TableId(value = "stream_id")
    private Long streamId;

    private LocalDateTime streamTime;

    private Long streamUserId;

    private Integer streamMoney;

    private Integer streamConsumeType;

    private String streamConsumeLocation;

    private String streamSignLocation;

    private Integer streamTimeDate;

    private String streamTimeMinute;

    private Integer streamSeconds;

}

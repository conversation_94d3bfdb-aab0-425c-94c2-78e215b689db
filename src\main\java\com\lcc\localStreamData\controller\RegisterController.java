package com.lcc.localStreamData.controller;

import com.lcc.localStreamData.common.R;
import com.lcc.localStreamData.entity.User;
import com.lcc.localStreamData.service.RegisterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class RegisterController {
    @Autowired
    RegisterService registerService;

    @PostMapping("/register")
    public R register(@RequestBody User user) {
        log.info("user:{}", user);
        return registerService.register(user);
    }
}

<template>
  <div class="model-deployment">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>模型发布与决策训练</span>
          <el-button type="primary" @click="deployModel" :loading="isDeploying">
            {{ isDeploying ? '部署中...' : '部署模型' }}
          </el-button>
        </div>
      </template>

      <!-- 模型管理 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="never">
            <template #header>
              <h4>模型列表</h4>
            </template>
            <el-table :data="models" style="width: 100%">
              <el-table-column prop="name" label="模型名称" />
              <el-table-column prop="version" label="版本" width="80" />
              <el-table-column prop="accuracy" label="准确率" width="100">
                <template #default="scope">
                  {{ scope.row.accuracy }}%
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)">
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="scope">
                  <el-button size="small" @click="selectModel(scope.row)">选择</el-button>
                  <el-button size="small" type="warning" @click="testModel(scope.row)">测试</el-button>
                  <el-button size="small" type="danger" @click="deleteModel(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card shadow="never">
            <template #header>
              <h4>部署配置</h4>
            </template>
            <el-form :model="deploymentConfig" label-width="120px">
              <el-form-item label="选中模型">
                <el-input v-model="selectedModel.name" readonly />
              </el-form-item>
              <el-form-item label="部署环境">
                <el-select v-model="deploymentConfig.environment">
                  <el-option label="开发环境" value="dev" />
                  <el-option label="测试环境" value="test" />
                  <el-option label="生产环境" value="prod" />
                </el-select>
              </el-form-item>
              <el-form-item label="实例数量">
                <el-input-number v-model="deploymentConfig.instances" :min="1" :max="10" />
              </el-form-item>
              <el-form-item label="内存限制">
                <el-select v-model="deploymentConfig.memory">
                  <el-option label="512MB" value="512m" />
                  <el-option label="1GB" value="1g" />
                  <el-option label="2GB" value="2g" />
                  <el-option label="4GB" value="4g" />
                </el-select>
              </el-form-item>
              <el-form-item label="CPU限制">
                <el-select v-model="deploymentConfig.cpu">
                  <el-option label="0.5核" value="0.5" />
                  <el-option label="1核" value="1" />
                  <el-option label="2核" value="2" />
                  <el-option label="4核" value="4" />
                </el-select>
              </el-form-item>
              <el-form-item label="自动扩缩">
                <el-switch v-model="deploymentConfig.autoScale" />
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>

      <!-- 部署状态 -->
      <div class="deployment-status" v-if="deploymentStatus.isDeployed">
        <el-card shadow="never">
          <template #header>
            <h4>部署状态</h4>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="status-item">
                <div class="status-label">服务状态</div>
                <el-tag :type="deploymentStatus.serviceStatus === 'running' ? 'success' : 'danger'" size="large">
                  {{ deploymentStatus.serviceStatus === 'running' ? '运行中' : '已停止' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="status-item">
                <div class="status-label">健康检查</div>
                <el-tag :type="deploymentStatus.healthCheck === 'healthy' ? 'success' : 'warning'" size="large">
                  {{ deploymentStatus.healthCheck === 'healthy' ? '健康' : '异常' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="status-item">
                <div class="status-label">响应时间</div>
                <span class="response-time">{{ deploymentStatus.responseTime }}ms</span>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 性能监控 -->
      <div class="performance-monitoring">
        <el-card shadow="never">
          <template #header>
            <h4>性能监控</h4>
          </template>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="请求总数" :value="performanceMetrics.totalRequests" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="成功率" :value="performanceMetrics.successRate" suffix="%" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="平均响应时间" :value="performanceMetrics.avgResponseTime" suffix="ms" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="QPS" :value="performanceMetrics.qps" />
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 决策训练 -->
      <div class="decision-training">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <h4>决策训练</h4>
              <el-button @click="startDecisionTraining" :loading="isTrainingDecision">
                {{ isTrainingDecision ? '训练中...' : '开始训练' }}
              </el-button>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <h5>训练配置</h5>
              <el-form :model="decisionConfig" label-width="120px">
                <el-form-item label="训练策略">
                  <el-select v-model="decisionConfig.strategy">
                    <el-option label="在线学习" value="online" />
                    <el-option label="批量学习" value="batch" />
                    <el-option label="增量学习" value="incremental" />
                  </el-select>
                </el-form-item>
                <el-form-item label="学习率">
                  <el-slider v-model="decisionConfig.learningRate" :min="0.001" :max="0.1" :step="0.001" />
                </el-form-item>
                <el-form-item label="反馈权重">
                  <el-slider v-model="decisionConfig.feedbackWeight" :min="0.1" :max="1" :step="0.1" />
                </el-form-item>
              </el-form>
            </el-col>
            <el-col :span="12">
              <h5>训练进度</h5>
              <div class="training-progress">
                <el-progress 
                  :percentage="decisionTrainingProgress" 
                  :status="decisionTrainingProgress === 100 ? 'success' : 'active'"
                />
                <div class="training-metrics">
                  <div class="metric-row">
                    <span>准确率提升:</span>
                    <span>+{{ decisionMetrics.accuracyImprovement }}%</span>
                  </div>
                  <div class="metric-row">
                    <span>误报率降低:</span>
                    <span>-{{ decisionMetrics.falsePositiveReduction }}%</span>
                  </div>
                  <div class="metric-row">
                    <span>训练样本数:</span>
                    <span>{{ decisionMetrics.trainingSamples }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- API 接口文档 -->
      <div class="api-documentation">
        <el-card shadow="never">
          <template #header>
            <h4>API 接口文档</h4>
          </template>
          <el-collapse>
            <el-collapse-item title="预测接口" name="predict">
              <div class="api-item">
                <p><strong>URL:</strong> POST /api/predict</p>
                <p><strong>描述:</strong> 对单笔交易进行风险预测</p>
                <p><strong>请求参数:</strong></p>
                <pre>{{ JSON.stringify(apiExamples.predictRequest, null, 2) }}</pre>
                <p><strong>响应示例:</strong></p>
                <pre>{{ JSON.stringify(apiExamples.predictResponse, null, 2) }}</pre>
              </div>
            </el-collapse-item>
            <el-collapse-item title="批量预测接口" name="batch-predict">
              <div class="api-item">
                <p><strong>URL:</strong> POST /api/batch-predict</p>
                <p><strong>描述:</strong> 对多笔交易进行批量风险预测</p>
                <p><strong>请求参数:</strong></p>
                <pre>{{ JSON.stringify(apiExamples.batchRequest, null, 2) }}</pre>
              </div>
            </el-collapse-item>
            <el-collapse-item title="模型状态接口" name="status">
              <div class="api-item">
                <p><strong>URL:</strong> GET /api/model/status</p>
                <p><strong>描述:</strong> 获取模型运行状态</p>
                <p><strong>响应示例:</strong></p>
                <pre>{{ JSON.stringify(apiExamples.statusResponse, null, 2) }}</pre>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const isDeploying = ref(false)
const isTrainingDecision = ref(false)
const decisionTrainingProgress = ref(0)

const models = ref([
  { id: 1, name: '欺诈检测模型v1.0', version: '1.0', accuracy: 92, status: '已训练' },
  { id: 2, name: '欺诈检测模型v1.1', version: '1.1', accuracy: 94, status: '已部署' },
  { id: 3, name: '欺诈检测模型v2.0', version: '2.0', accuracy: 96, status: '测试中' }
])

const selectedModel = ref({ name: '请选择模型' })

const deploymentConfig = reactive({
  environment: 'test',
  instances: 2,
  memory: '2g',
  cpu: '2',
  autoScale: true
})

const deploymentStatus = reactive({
  isDeployed: false,
  serviceStatus: 'stopped',
  healthCheck: 'unknown',
  responseTime: 0
})

const performanceMetrics = reactive({
  totalRequests: 12580,
  successRate: 98.5,
  avgResponseTime: 45,
  qps: 156
})

const decisionConfig = reactive({
  strategy: 'online',
  learningRate: 0.01,
  feedbackWeight: 0.8
})

const decisionMetrics = reactive({
  accuracyImprovement: 0,
  falsePositiveReduction: 0,
  trainingSamples: 0
})

const apiExamples = reactive({
  predictRequest: {
    transaction_id: "TXN123456",
    amount: 5000,
    merchant: "某商户",
    location: "北京",
    timestamp: "2023-12-01T10:30:00Z",
    user_id: "USER001"
  },
  predictResponse: {
    transaction_id: "TXN123456",
    risk_score: 0.85,
    prediction: "high_risk",
    confidence: 0.92,
    reasons: ["金额异常", "地理位置异常"]
  },
  batchRequest: {
    transactions: [
      { transaction_id: "TXN123456", amount: 5000 },
      { transaction_id: "TXN123457", amount: 1200 }
    ]
  },
  statusResponse: {
    model_name: "欺诈检测模型v1.1",
    status: "running",
    uptime: "2天3小时",
    requests_processed: 12580
  }
})

const getStatusType = (status) => {
  switch (status) {
    case '已部署': return 'success'
    case '测试中': return 'warning'
    case '已训练': return 'info'
    default: return 'info'
  }
}

const selectModel = (model) => {
  selectedModel.value = model
  ElMessage.success(`已选择模型: ${model.name}`)
}

const testModel = async (model) => {
  ElMessage.info(`正在测试模型: ${model.name}`)
  // 模拟测试过程
  setTimeout(() => {
    ElMessage.success('模型测试通过')
  }, 2000)
}

const deleteModel = async (model) => {
  try {
    await ElMessageBox.confirm(`确定要删除模型 ${model.name} 吗？`, '确认删除', {
      type: 'warning'
    })
    
    const index = models.value.findIndex(m => m.id === model.id)
    if (index > -1) {
      models.value.splice(index, 1)
      ElMessage.success('模型删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

const deployModel = async () => {
  if (!selectedModel.value.name || selectedModel.value.name === '请选择模型') {
    ElMessage.warning('请先选择要部署的模型')
    return
  }
  
  isDeploying.value = true
  ElMessage.info('开始部署模型...')
  
  // 模拟部署过程
  setTimeout(() => {
    isDeploying.value = false
    deploymentStatus.isDeployed = true
    deploymentStatus.serviceStatus = 'running'
    deploymentStatus.healthCheck = 'healthy'
    deploymentStatus.responseTime = 45
    
    ElMessage.success('模型部署成功！')
  }, 3000)
}

const startDecisionTraining = () => {
  if (isTrainingDecision.value) return
  
  isTrainingDecision.value = true
  decisionTrainingProgress.value = 0
  
  ElMessage.info('开始决策训练...')
  
  const trainingInterval = setInterval(() => {
    decisionTrainingProgress.value += Math.floor(Math.random() * 15) + 5
    
    // 更新训练指标
    decisionMetrics.accuracyImprovement = Math.floor(decisionTrainingProgress.value * 0.05)
    decisionMetrics.falsePositiveReduction = Math.floor(decisionTrainingProgress.value * 0.03)
    decisionMetrics.trainingSamples = Math.floor(decisionTrainingProgress.value * 50)
    
    if (decisionTrainingProgress.value >= 100) {
      decisionTrainingProgress.value = 100
      clearInterval(trainingInterval)
      isTrainingDecision.value = false
      
      ElMessage.success('决策训练完成！')
    }
  }, 500)
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.deployment-status {
  margin: 20px 0;
}

.status-item {
  text-align: center;
  padding: 20px;
}

.status-label {
  margin-bottom: 10px;
  color: #666;
  font-weight: bold;
}

.response-time {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.performance-monitoring {
  margin: 20px 0;
}

.decision-training {
  margin: 20px 0;
}

.training-progress {
  padding: 20px 0;
}

.training-metrics {
  margin-top: 20px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 5px 0;
}

.api-documentation {
  margin-top: 20px;
}

.api-item {
  padding: 15px;
}

.api-item pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>

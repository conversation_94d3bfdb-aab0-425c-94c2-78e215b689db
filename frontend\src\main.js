import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './style.css'
import App from './App.vue'
import router from './router'
import axios from 'axios'
// 配置 axios
axios.defaults.baseURL = ''
axios.defaults.timeout = 10000

const app = createApp(App)

app.use(ElementPlus)
app.use(router)
app.config.globalProperties.$http = axios

app.mount('#app')

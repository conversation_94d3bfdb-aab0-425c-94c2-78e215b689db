package com.lcc.localStreamData.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lcc.localStreamData.entity.Dataset;
import com.lcc.localStreamData.entity.Rule;

import java.util.List;

public interface RuleService extends IService<Rule> {
    List<Rule> findListByRuleGroupId(Integer id);

    void removeOldRules(Integer ruleGroupId, List<Integer> newRuleIdList);

}
